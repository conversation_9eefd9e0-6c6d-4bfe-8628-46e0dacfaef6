import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { fetchAvatarUrl } from '@/api';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const formatDateForAPI = (date: Date | null): string => {
  if (!date) {
    return '';
  }
  return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
};

export const calculatePercentage = (value: number, total: number): string => {
  if (total === 0) {
    return '0.0%';
  }
  return ( ( value / total ) * 100 ).toFixed(1) + '%';
};
//

export const fetchAvatarAuthor = async (authorId: string, authorName: string = 'Unknown'): Promise<{avatarUrl: string}> => {
  try {
    const timeoutPromise = new Promise<string>((_, reject) =>
      setTimeout(() => reject(new Error('Avatar fetch timeout')), 10000)
    );
    const avatarPromise = fetchAvatarUrl(authorId, `Author ${authorId}`);
    const avatarUrl = await Promise.race([avatarPromise, timeoutPromise]);
    return { avatarUrl };
  } catch (error) {
    const fallbackUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(`Author ${authorId}`)}&background=random&size=40`;
    return { avatarUrl: fallbackUrl };
  }
}
