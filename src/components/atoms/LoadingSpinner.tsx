import React from 'react';

interface LoadingIndicatorProps {
  text?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showText?: boolean;
}

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  text = 'Loading',
  size = 'md',
  className = '',
  showText = true
}) => {
  const sizeClasses = {
    sm: {
      icon: 'w-4 h-4',
      text: 'text-xs',
      container: 'gap-2'
    },
    md: {
      icon: 'w-6 h-6',
      text: 'text-sm',
      container: 'gap-3'
    },
    lg: {
      icon: 'w-8 h-8',
      text: 'text-base',
      container: 'gap-4'
    }
  };

  const currentSize = sizeClasses[size];

  // Spinner Icon Component - Circle with gap like the image
  const SpinnerIcon = () => (
    <div className={`${currentSize.icon} relative`}>
      <div className="absolute inset-0 rounded-full border-2 border-gray-200"></div>
      <div className="absolute inset-0 rounded-full border-2 border-blue-500 border-t-transparent animate-spin"></div>
    </div>
  );



  // Animated Dots Text Component
  const AnimatedDots = () => (
    <span className="inline-block ml-1">
      <span className="loading-dots">.</span>
      <span className="loading-dots">.</span>
      <span className="loading-dots">.</span>
    </span>
  );

  return (
    <div className={`flex items-center justify-center ${currentSize.container} ${className}`}>
      <SpinnerIcon />
      {showText && (
        <span className={`text-slate-600 font-medium ${currentSize.text}`}>
          {text}
          <AnimatedDots />
        </span>
      )}
    </div>
  );
};

export default LoadingIndicator;
