import React from 'react';
import { cn } from '@/lib/utils';

export interface DateInputProps {
  value: string;
  placeholder?: string;
  className?: string;
  readOnly?: boolean;
}

export const DateInput: React.FC<DateInputProps> = ({
  value,
  placeholder = "MM/DD/YYYY",
  className,
  readOnly = true
}) => {
  return (
    <div className={cn(
      "flex h-9 items-center p-2 bg-[#fdfdfd] rounded-xl border border-[#c5c8cb]",
      className
    )}>
      <span className="font-bodytext-sm-md text-[#909498]">
        {value || placeholder}
      </span>
    </div>
  );
};
