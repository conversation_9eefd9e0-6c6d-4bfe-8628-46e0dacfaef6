import React from 'react';
import useAvatar from '@/hooks/useAvatar';
import { cn } from '@/lib/utils';
import LoadingIndicator from '@/components/atoms/LoadingSpinner';

type TAvatar = {
  uid: string;
  name: string;
  className?: string;
};
export const AvatarFb: React.FC<TAvatar> = ({ ...props }: TAvatar) => {
  const { uid, name, className } = props;
  const { avatar, loading } = useAvatar(uid, name);
  return (!uid || !avatar) ? (
    <div
      className={cn(
        'flex items-center justify-center font-bold text-lg text-white w-[32px] h-[32px] rounded-full',
        className,
      )}
    >
      {name ? name.charAt(0).toUpperCase() : ''}
    </div>
  ) : loading ? (
    <LoadingIndicator size="sm" showText={false}/>
  ) : (
    <img
      className={cn('w-[32px] h-[32px] rounded-full', className)}
      src={avatar}
      alt={'fb_avatar'}
    />
  );
};
