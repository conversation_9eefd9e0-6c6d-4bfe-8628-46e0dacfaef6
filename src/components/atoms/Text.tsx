import React from 'react';
import { cn } from '@/lib/utils';

export interface TextProps {
  children: React.ReactNode;
  variant?: 'h1' | 'h2' | 'h3' | 'body-md' | 'body-sm' | 'body-sm-semibold' | 'body-md-md';
  color?: 'primary' | 'secondary' | 'muted' | 'success' | 'error';
  className?: string;
}

const textVariants = {
  h1: 'font-heading-h1 text-[length:var(--heading-h1-font-size)] tracking-[var(--heading-h1-letter-spacing)] leading-[var(--heading-h1-line-height)] font-[number:var(--heading-h1-font-weight)] [font-style:var(--heading-h1-font-style)]',
  h2: 'font-heading-h2',
  h3: 'font-heading-h3 font-[number:var(--heading-h3-font-weight)] text-[length:var(--heading-h3-font-size)] tracking-[var(--heading-h3-letter-spacing)] leading-[var(--heading-h3-line-height)] [font-style:var(--heading-h3-font-style)]',
  'body-md': 'font-bodytext-md-md font-[number:var(--bodytext-md-md-font-weight)] text-[length:var(--bodytext-md-md-font-size)] tracking-[var(--bodytext-md-md-letter-spacing)] leading-[var(--bodytext-md-md-line-height)] [font-style:var(--bodytext-md-md-font-style)]',
  'body-sm': 'font-bodytext-sm-md font-[number:var(--bodytext-sm-md-font-weight)] text-[length:var(--bodytext-sm-md-font-size)] tracking-[var(--bodytext-sm-md-letter-spacing)] leading-[var(--bodytext-sm-md-line-height)] [font-style:var(--bodytext-sm-md-font-style)]',
  'body-sm-semibold': 'font-bodytext-sm-semibold font-[number:var(--bodytext-sm-semibold-font-weight)] text-[length:var(--bodytext-sm-semibold-font-size)] tracking-[var(--bodytext-sm-semibold-letter-spacing)] leading-[var(--bodytext-sm-semibold-line-height)] [font-style:var(--bodytext-sm-semibold-font-style)]',
  'body-md-md': 'font-bodytext-md-md text-[length:var(--bodytext-md-md-font-size)] tracking-[var(--bodytext-md-md-letter-spacing)] leading-[var(--bodytext-md-md-line-height)]'
};

const colorVariants = {
  primary: 'text-[#141416]',
  secondary: 'text-[#4e5255]',
  muted: 'text-[#909498]',
  success: 'text-[#2bb684]',
  error: 'text-[#f84242]'
};

export const Text: React.FC<TextProps> = ({ 
  children, 
  variant = 'body-sm', 
  color = 'primary', 
  className 
}) => {
  const Component = variant.startsWith('h') ? variant as 'h1' | 'h2' | 'h3' : 'span';
  
  return (
    <Component 
      className={cn(
        textVariants[variant],
        colorVariants[color],
        className
      )}
    >
      {children}
    </Component>
  );
};
