import React from 'react';
import { Text } from '@/components/atoms/Text';
import { ActionButton } from '@/components/molecules/ActionButton';
import { DownloadIcon } from 'lucide-react';

export interface HeaderProps {
  title?: string;
  showDownloadButton?: boolean;
  onDownload?: () => void;
}

export const Header: React.FC<HeaderProps> = ({
  title,
  showDownloadButton = true,
  onDownload
}) => {
  return (
    <header className="flex items-center justify-between py-4 w-full">
      <Text variant="h1" color="primary">
        {title}
      </Text>

      {showDownloadButton && (
        <ActionButton
          icon={DownloadIcon}
          variant="outline"
          onClick={onDownload}
          className="bg-[#fdfdfd] border-[#c5c8cb] rounded-2xl"
        >
          Download
        </ActionButton>
      )}
    </header>
  );
};
