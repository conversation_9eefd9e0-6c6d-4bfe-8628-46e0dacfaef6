import React from 'react';
import { CampaignCard } from '@/components/molecules/CampaignCard';
import { Text } from '@/components/atoms/Text';
import { cn } from '@/lib/utils';

export interface CampaignGridItem {
  id: string;
  title: string;
  status: 'RUNNING' | 'PAUSED' | 'COMPLETED' | 'DRAFT';
  keywords: string[];
  moreKeywordsCount?: number;
  createdDate: string;
}

export interface CampaignGridProps {
  campaigns: CampaignGridItem[];
  totalCount: number;
  onPreview?: (id: string) => void;
  className?: string;
}

export const CampaignGrid: React.FC<CampaignGridProps> = ({
  campaigns,
  totalCount,
  onPreview,
  className
}) => {
  return (
    <div className={cn('w-full', className)}>
      <div className="flex items-center justify-between mb-8">
        <Text variant="h2" className="text-[#111827] font-bold text-2xl">
          Your Campaign
        </Text>
        <Text variant="body-sm" color="secondary">
          {totalCount} campaigns
        </Text>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {campaigns.map((campaign) => (
          <CampaignCard
            key={campaign.id}
            id={campaign.id}
            title={campaign.title}
            status={campaign.status}
            keywords={campaign.keywords}
            moreKeywordsCount={campaign.moreKeywordsCount}
            createdDate={campaign.createdDate}
            onPreview={onPreview}
          />
        ))}
      </div>
    </div>
  );
};
