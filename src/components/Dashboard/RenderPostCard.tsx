import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import React from 'react';
import { AvatarFb } from '@/components/atoms/AvatarFb';
import { PostIntentData } from '@/types/campaign';
import { useCampaignKeywords } from '@/contexts/CampaignKeywordsContext';

interface RenderPostCardProps {
  post: PostIntentData;
}

export const RenderPostCard = ({ post }: RenderPostCardProps) => {
  const { highlightKeywords } = useCampaignKeywords();

  return <div
    key={post.id}
    className="flex flex-col items-start gap-2.5 relative self-stretch w-full"
  >
    <Card className="flex flex-col items-start gap-4 p-4 w-full bg-[#fdfdfd] border-[#c5c8cb]">
      <CardContent className="p-0 w-full space-y-4">
        <div className="flex items-start justify-between w-full">
          <div className="flex items-center gap-2">
            <AvatarFb uid={post.id.toString()} name={post.author} />
            <div className="flex flex-col items-start gap-1">
              <div className="font-bodytext-sm-md text-[#141416] text-[length:var(--bodytext-sm-md-font-size)] tracking-[var(--bodytext-sm-md-letter-spacing)] leading-[var(--bodytext-sm-md-line-height)]">
                {post.author}
              </div>
              <div className="font-bodytext-xs-reg text-[#909498] text-[length:var(--bodytext-xs-reg-font-size)] tracking-[var(--bodytext-xs-reg-letter-spacing)] leading-[var(--bodytext-xs-reg-line-height)]">
                {post.time} • {post.platform}
              </div>
            </div>
          </div>
          <div className="flex items-center">
            <img className="w-6 h-6" alt="Emotion" src={post.sentimentIcon} />
          </div>
        </div>

        <div className="font-bodytext-sm-reg text-[#4e5255] text-[length:var(--bodytext-sm-reg-font-size)] tracking-[var(--bodytext-sm-reg-letter-spacing)] leading-[var(--bodytext-sm-reg-line-height)]">
          {highlightKeywords(post.content)}
        </div>

        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-3 h-5">
            <div className="flex items-center gap-1">
              <img
                className="w-5 h-5"
                alt="Likes"
                src="/figmaAssets/thumb-up-line.svg"
              />
              <span className="font-bodytext-xs-reg text-[#515667] text-[length:var(--bodytext-xs-reg-font-size)] text-center tracking-[var(--bodytext-xs-reg-letter-spacing)] leading-[var(--bodytext-xs-reg-line-height)]">
                  {post.likes}
                </span>
            </div>
            <div className="flex items-center gap-1">
              <img
                className="w-5 h-5"
                alt="Comments"
                src="/figmaAssets/chat-3-line.svg"
              />
              <span className="font-bodytext-xs-reg text-[#515667] text-[length:var(--bodytext-xs-reg-font-size)] text-center tracking-[var(--bodytext-xs-reg-letter-spacing)] leading-[var(--bodytext-xs-reg-line-height)]">
                  {post.comments}
                </span>
            </div>
            <div className="flex items-center gap-1">
              <img
                className="w-5 h-5"
                alt="Shares"
                src="/figmaAssets/share-forward-line.svg"
              />
              <span className="font-bodytext-xs-reg text-[#515667] text-[length:var(--bodytext-xs-reg-font-size)] text-center tracking-[var(--bodytext-xs-reg-letter-spacing)] leading-[var(--bodytext-xs-reg-line-height)]">
                  {post.shares}
                </span>
            </div>
          </div>
          <Button
            variant="ghost"
            className="p-1 rounded-lg flex items-center gap-1"
          >
              <span className="font-bodytext-sm-md text-[#7c47e6] text-[length:var(--bodytext-sm-md-font-size)] tracking-[var(--bodytext-sm-md-letter-spacing)] leading-[var(--bodytext-sm-md-line-height)]">
                Visit
              </span>
            <img
              className="w-5 h-5"
              alt="External link"
              src="/figmaAssets/external-link-line.svg"
            />
          </Button>
        </div>
      </CardContent>
    </Card>
  </div>;
};
