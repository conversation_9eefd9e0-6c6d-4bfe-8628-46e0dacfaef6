import { Card } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { AvatarFb } from '@/components/atoms/AvatarFb';
import React, { useEffect, useState } from 'react';
import { DateRange } from '@/components';
import { RenderPostCard } from '@/components/Dashboard/RenderPostCard';
import {
  PostIntentData,
  TopInfluencersByMentionsResponse,
  TopInfluencersData,
  TopPostsByMentionsResponse
} from '@/types/campaign';
import { campaignService } from '@/api';
import { useParams } from 'wouter';
import { formatDateForAPI, calculatePercentage } from '@/lib/utils';

interface UserIntentClassificationProps {
  dateRange?: DateRange;
}

export const TopMentions = ({ dateRange }: UserIntentClassificationProps): JSX.Element => {
  const params = useParams();
  const campaignId = params.id;

  // Render a social media post card
  // State for Top Mentions
  const [topMentionsPosts, setTopMentionsPosts] = useState<PostIntentData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for Top Influencers
  const [topInfluencers, setTopInfluencers] = useState<TopInfluencersData[]>([]);
  const [influencersLoading, setInfluencersLoading] = useState(false);
  const [influencersError, setInfluencersError] = useState<string | null>(null);

  // State for active tab
  const [activeTab, setActiveTab] = useState('top-mentions');

  // Helper functions
  const formatPostTime = (postTime: string): string => {
    const date = new Date(postTime);
    const now = new Date();
    const diffInHours = Math.floor(( now.getTime() - date.getTime() ) / ( 1000 * 60 * 60 ));

    if (diffInHours < 1) {
      return 'just now';
    }
    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? '' : ''} ago`;
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }

    return date.toLocaleDateString('vi-VN');
  };

  const getSentimentIcon = (sentiment: string): string => {
    switch (sentiment.toUpperCase()) {
      case 'POSITIVE':
        return '/figmaAssets/emotion-happy-line.svg';
      case 'NEGATIVE':
        return '/figmaAssets/emotion-unhappy-line.svg';
      case 'NEUTRAL':
      default:
        return '/figmaAssets/emotion-normal-line.svg';
    }
  };

  const getTopPostsByMentions = async () => {
    if (!campaignId || !dateRange?.start || !dateRange?.end) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const from_date = formatDateForAPI(dateRange.start);
      const to_date = formatDateForAPI(dateRange.end);

      const response = await campaignService.getCampaignReport({
        id: campaignId,
        endpoint: 'report/top-posts-by-mentions',
        from_date,
        to_date
      });

      const dataResponse = response as any as TopPostsByMentionsResponse[];
      if (response) {
        const apiData = dataResponse as TopPostsByMentionsResponse[];
        // Transform API data to component format
        const transformedPosts: PostIntentData[] = apiData.map((post, index) => ( {
          id: post.author_id,
          author: post.author_name,
          avatar: '/figmaAssets/img-1.png', // Default avatar
          time: formatPostTime(post.post_time),
          platform: 'Social Media',
          sentiment: post.sentiment.toLowerCase(),
          sentimentIcon: getSentimentIcon(post.sentiment),
          content: post.content,
          likes: post.engagement.like,
          comments: post.engagement.comment,
          shares: post.engagement.share,
          highlightPosition: { left: '149px', width: '72px' }
        } )) as any || [];

        setTopMentionsPosts(transformedPosts);
      } else {
        setTopMentionsPosts([]);
      }
    } catch (err) {
      setError('Failed to load top mentions data');
    } finally {
      setLoading(false);
    }
  };

  const formatFollowers = (mentions: number): string => {
    // Since API doesn't provide followers, we'll estimate based on mentions
    // This is a placeholder - in real scenario you'd have actual follower data
    const estimated = mentions * 1000; // Rough estimation

    if (estimated >= 1000000) {
      return ( estimated / 1000000 ).toFixed(1) + 'M';
    } else if (estimated >= 1000) {
      return ( estimated / 1000 ).toFixed(0) + 'K';
    }
    return estimated.toString();
  };

  const calculateSentimentPercentages = (sentiment: {positive: number; negative: number; neutral: number}) => {
    const total = sentiment.positive + sentiment.negative + sentiment.neutral;
    if (total === 0) {
      return { positive: 0, negative: 0, neutral: 0 };
    }

    return {
      positive: Math.round(( sentiment.positive / total ) * 100),
      negative: Math.round(( sentiment.negative / total ) * 100),
      neutral: Math.round(( sentiment.neutral / total ) * 100)
    };
  };

  const getTopInfluencersByMentions = async () => {
    if (!campaignId || !dateRange?.start || !dateRange?.end) {
      return;
    }

    try {
      setInfluencersLoading(true);
      setInfluencersError(null);

      const from_date = formatDateForAPI(dateRange.start);
      const to_date = formatDateForAPI(dateRange.end);

      const response = await campaignService.getCampaignReport({
        id: campaignId,
        endpoint: 'report/top-influencers-by-mentions',
        from_date,
        to_date
      });

      let apiData: TopInfluencersByMentionsResponse[] = [];

      if (response.data && Array.isArray(response.data)) {
        apiData = response.data as TopInfluencersByMentionsResponse[];
      } else if (Array.isArray(response)) {
        apiData = response as TopInfluencersByMentionsResponse[];
      }

      if (apiData.length > 0) {
        const transformedInfluencers: TopInfluencersData[] = apiData.map((influencer, index) => {
          const sentimentPercentages = calculateSentimentPercentages(influencer.sentiment);

          return {
            id: influencer.author_id,
            name: influencer.name,
            handle: `@${influencer.name.toLowerCase().replace(/\s+/g, '')}`,
            avatar: `/figmaAssets/img-${( index % 7 ) + 1}.png`,
            followers: formatFollowers(influencer.mentions),
            mentions: influencer.mentions,
            sentimentPercentages: sentimentPercentages
          };
        }) as TopInfluencersData[] || [];

        setTopInfluencers(transformedInfluencers);
      } else {
        setTopInfluencers([]);
      }
    } catch (err) {
      setInfluencersError('Failed to load top influencers data');
    } finally {
      setInfluencersLoading(false);
    }
  };

  useEffect(() => {
    if (activeTab === 'top-mentions') {
      getTopPostsByMentions();
    } else if (activeTab === 'top-influencers') {
      getTopInfluencersByMentions();
    }
  }, [campaignId, dateRange, activeTab]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };
  return (
    <>
      {/* Left panel - Mentioned Posts */}
      <Card className="flex-1 gap-3 p-4 bg-[#fdfdfd] rounded-2xl border-[#c5c8cb]">
        <div className="font-bodytext-md-md text-[#4e5255] text-[length:var(--bodytext-md-md-font-size)] tracking-[var(--bodytext-md-md-letter-spacing)] leading-[var(--bodytext-md-md-line-height)]">
          Mentioned Posts
        </div>

        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <TabsList className="flex h-10 gap-2 w-full border-b border-[#c5c8cb] bg-transparent p-0">
            <TabsTrigger
              value="top-mentions"
              className="flex-1 rounded-none border-b-2 border-[#7c47e6] data-[state=active]:border-[#7c47e6] data-[state=active]:text-[#7c47e6] data-[state=inactive]:border-[#c5c8cb] data-[state=inactive]:text-[#4e5255] pb-2 pt-0 px-1 bg-transparent"
            >
              <div className="font-bodytext-sm-md px-2 py-1">Top Mentions</div>
            </TabsTrigger>
            <TabsTrigger
              value="top-influencers"
              className="flex-1 rounded-none border-b-2 border-[#c5c8cb] data-[state=active]:border-[#7c47e6] data-[state=active]:text-[#7c47e6] data-[state=inactive]:border-[#c5c8cb] data-[state=inactive]:text-[#4e5255] pb-2 pt-0 px-1 bg-transparent"
            >
              <div className="font-bodytext-sm-md px-2 py-1">
                Top influencers
              </div>
            </TabsTrigger>
          </TabsList>

          <TabsContent
            value="top-mentions"
            className="mt-4 space-y-4 overflow-hidden h-[1030px] pr-3 overflow-y-auto"
          >
            {!dateRange?.start || !dateRange?.end ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-[#909498] font-bodytext-sm-md">Please select a date range to view top mentions</div>
              </div>
            ) : loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-[#909498] font-bodytext-sm-md">Loading top mentions...</div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-red-500 font-bodytext-sm-md">{error}</div>
              </div>
            ) : topMentionsPosts.length === 0 ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-[#909498] font-bodytext-sm-md">No mentions found for this period</div>
              </div>
            ) : (
              topMentionsPosts.map((post) => <RenderPostCard post={post} />)
            )}
          </TabsContent>

          <TabsContent value="top-influencers" className="mt-4">
            {/* Top Influencers Table */}
            <div className="w-full">
              {/* Table Header */}
              <div className="flex items-center justify-between py-3 px-4 border-b border-[#e5e7eb]">
                <div className="flex-1 font-bodytext-sm-md text-[#6b7280]">Influencer</div>
                <div className="w-20 text-center font-bodytext-sm-md text-[#6b7280]">Followers</div>
                <div className="w-20 text-center font-bodytext-sm-md text-[#6b7280]">Mentions</div>
                <div className="w-24 text-center font-bodytext-sm-md text-[#6b7280]">Sentiment</div>
              </div>

              {/* Table Rows */}
              <div className="space-y-0">
                {!dateRange?.start || !dateRange?.end ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="text-[#909498] font-bodytext-sm-md">Please select a date range to view top influencers</div>
                  </div>
                ) : influencersLoading ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="text-[#909498] font-bodytext-sm-md">Loading top influencers...</div>
                  </div>
                ) : influencersError ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="text-red-500 font-bodytext-sm-md">{influencersError}</div>
                  </div>
                ) : topInfluencers.length === 0 ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="text-[#909498] font-bodytext-sm-md">No influencers found for this period</div>
                  </div>
                ) : (
                  topInfluencers.map((influencer, index) => (
                    <div
                      key={influencer.id}
                      className={`flex items-center justify-between py-4 px-4 ${index < topInfluencers.length - 1 ?
                        'border-b border-[#f3f4f6]' :
                        ''}`}
                    >
                      <div className="flex items-center gap-3 flex-1">
                        <AvatarFb uid={influencer.id} name={influencer.name} />
                        <div className="flex flex-col">
                          <div className="font-bodytext-sm-md text-[#141416]">{influencer.name}</div>
                          <div className="font-bodytext-xs-reg text-[#6b7280]">{influencer.handle}</div>
                        </div>
                      </div>
                      <div className="w-20 text-center font-bodytext-sm-md text-[#141416]">{influencer.followers}</div>
                      <div className="w-20 text-center font-bodytext-sm-md text-[#141416]">{influencer.mentions}</div>
                      <div className="w-24 flex justify-center">
                        <div className="flex items-center w-24 h-2 bg-[#f3f4f6] overflow-hidden">
                          <div
                            className="h-full bg-[#2BB684]"
                            style={{ width: `${influencer.sentimentPercentages.positive}%` }}
                          ></div>
                          <div
                            className="h-full bg-[#F84242]"
                            style={{ width: `${influencer.sentimentPercentages.negative}%` }}
                          ></div>
                          <div
                            className="h-full bg-[#C5C8CB]"
                            style={{ width: `${influencer.sentimentPercentages.neutral}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </>
  );
};
