import { TargetIcon } from 'lucide-react';
import { DateRange } from '@/components';
import { Card, CardContent } from '@/components/ui/card';
import React, { useEffect, useState } from 'react';
import { useParams } from 'wouter';
import campaignService from '@/api/services/campaignService';
import { TotalSourceResponse } from '@/types/campaign';
import { formatDateForAPI } from '@/lib/utils';
import { RiArrowLeftDownLine, RiArrowRightUpLine } from '@remixicon/react';

interface TotalSourceProps {
  dateRange?: DateRange;
}

export const TotalSource = ({ dateRange }: TotalSourceProps) => {
  const params = useParams();
  const campaignId = params.id;

  // State for Total Source data
  const [sourceData, setSourceData] = useState({
    value: '123',
    subtitle: 'Page/Group/Profile',
    trend: undefined as {value: string; direction: 'up' | 'down'; description: string} | undefined
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const calculateTrendDirection = (current: number, previous: number): 'up' | 'down' => {
    return current >= previous ? 'up' : 'down';
  };

  const formatPercentage = (pct: number): string => {
    return Math.abs(pct).toFixed(2) + '%';
  };

  const getTotalSource = async () => {
    if (!campaignId || !dateRange?.start || !dateRange?.end) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const from_date = formatDateForAPI(dateRange.start);
      const to_date = formatDateForAPI(dateRange.end);

      const response = await campaignService.getCampaignReport({
        id: campaignId,
        endpoint: 'report/total-source',
        from_date,
        to_date
      });

      const dataResponse = response as any as TotalSourceResponse;
      if (response) {
        const apiData = dataResponse as TotalSourceResponse;

        // Calculate trend direction and format data
        const direction = calculateTrendDirection(apiData.curr, apiData.prev);
        const trendValue = formatPercentage(apiData.pct);

        setSourceData({
          value: apiData.curr.toLocaleString(),
          subtitle: 'Page/Group/Profile',
          trend: {
            value: trendValue,
            direction: direction,
            description: 'compared to previous period'
          }
        });
      }
    } catch (err) {
      setError('Failed to load total source data');
      // Keep default data on error
      setSourceData({
        value: '123',
        subtitle: 'Page/Group/Profile',
        trend: undefined
      });
    } finally {
      setLoading(false);
    }
  };

  // Call API when component mounts or when dateRange changes
  useEffect(() => {
    getTotalSource();
  }, [campaignId, dateRange]);
  return (
    <Card className="flex-1 border border-[#c5c8cb] rounded-[26px] bg-[#fdfdfd]">
      <CardContent className="flex flex-col items-start gap-4 p-6 h-full justify-between">
        {/* Header with title and icon */}
        <div className="flex items-start justify-between w-full">
          <div className="flex flex-col gap-1">
            <h3 className="font-bodytext-md-md text-[#4E5255]">Total Source</h3>
            <p className="font-bodytext-sm-reg text-[#909498] leading-relaxed text-sm">
              {!dateRange?.start || !dateRange?.end
                ? "Please select a date range"
                : loading
                ? "Loading..."
                : error
                ? "Error loading data"
                : sourceData.subtitle
              }
            </p>
          </div>
          <div className="w-6 h-6 text-[#141416]">
            <TargetIcon size={24} />
          </div>
        </div>

        {/* Main value */}
        <div className="flex flex-col gap-2">
          <div className="font-heading-h1 text-3xl text-[#141416]">
            {!dateRange?.start || !dateRange?.end
              ? "--"
              : loading
              ? "--"
              : error
              ? "--"
              : sourceData.value
            }
          </div>
        </div>
        {/* Trend indicator */}
        {sourceData.trend && !loading && !error && (
          <div className="flex items-center gap-1">
            <div className={`flex items-center gap-1 text-sm ${
              sourceData.trend.direction === 'up' ? 'text-[#2bb684]' : 'text-[#f84242]'
            }`}>
              {sourceData.trend.direction === 'up' ? <RiArrowRightUpLine size={24}/>:<RiArrowLeftDownLine size={24}/>}
              <span className="font-bodytext-sm-md">{sourceData.trend.value}</span>
            </div>
            <span className="font-bodytext-sm-reg text-[#4E5255] text-sm">
                {sourceData.trend.description}
              </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
