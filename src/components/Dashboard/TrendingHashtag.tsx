import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { DateRange } from '@/components/molecules/DateRangePicker';
import { useParams } from 'wouter';
import { campaignService, TopicCloudResponse } from '@/api';
import { TrendingHashtagResponse } from '@/types/campaign';

interface TrendingHashtagProps {
  dateRange?: DateRange;
}

interface HashtagItem {
  name: string;
  mentions: string;
}

export const TrendingHashtag = ({ dateRange }: TrendingHashtagProps) => {
  const params = useParams();
  const campaignId = params.id;

  const [trendingHashtags, setTrendingHashtags] = useState<HashtagItem[]>([
    { name: '', mentions: '' },
  ]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const formatDateForAPI = (date: Date | null): string => {
    if (!date) return '';
    return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
  };

  const getTrendingHashtags = async () => {
    if (!campaignId || !dateRange?.start || !dateRange?.end) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const from_date = formatDateForAPI(dateRange.start);
      const to_date = formatDateForAPI(dateRange.end);

      const response = await campaignService.getCampaignReport({
        id: campaignId,
        endpoint: 'report/trending-hashtags',
        from_date,
        to_date
      });

      const dataResponse = response as any as TrendingHashtagResponse;

      if (dataResponse) {
        const hashtagData = dataResponse as TrendingHashtagResponse;
        // Convert API response to hashtag format
        const convertedData = hashtagData.labels.map((label, index) => ({
          name: label.startsWith('#') ? label : `#${label}`,
          mentions: `${hashtagData.values[index]} mentions`
        }));
        setTrendingHashtags(convertedData);
      }
    } catch (err) {
      console.error('Error fetching trending hashtags:', err);
      setError('Failed to load trending hashtags');
    } finally {
      setLoading(false);
    }
  };

  // Call API when component mounts or when dateRange changes
  useEffect(() => {
    getTrendingHashtags();
  }, [campaignId, dateRange]);

  return (
    <>
      {/* Trending Hashtag Card */}
      <Card className="flex flex-col w-[335px] items-start gap-6 p-6 bg-[#fdfdfd] rounded-[26px] border border-solid border-[#c5c8cb] h-[540px]">
        <CardHeader className="flex items-start justify-between p-0 pb-2 w-full border-b border-[#c5c8cb]">
          <div className="inline-flex flex-col items-start justify-center gap-1">
            <h3 className="font-bodytext-md-md text-[#4e5255]">
              Trending Hashtag
            </h3>
          </div>
        </CardHeader>
        <CardContent className="p-0 space-y-6 w-full flex-1 overflow-auto pr-4">
          {!dateRange?.start || !dateRange?.end ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-[#909498] font-bodytext-sm-md">Please select a date range to view hashtags</div>
            </div>
          ) : loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-[#909498] font-bodytext-sm-md">Loading hashtags...</div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-red-500 font-bodytext-sm-md">{error}</div>
            </div>
          ) : (
            trendingHashtags.map((hashtag, index) => (
              <div
                key={index}
                className="flex items-start justify-between w-full"
              >
                <div className="font-bodytext-sm-md text-[#141416]">
                  {hashtag.name}
                </div>
                <div className="font-bodytext-xs-reg text-[#909498]">
                  {hashtag.mentions}
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>
    </>
  );
};
