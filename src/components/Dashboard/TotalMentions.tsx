import { LineChartIcon } from 'lucide-react';
import { DateRange, StatCard } from '@/components';
import React, { useEffect, useState } from 'react';
import { useParams } from 'wouter';
import campaignService from '@/api/services/campaignService';
import { TotalEngagementResponse, TotalMentionsResponse } from '@/types/campaign';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { ChartContainer } from '@/components/atoms/ChartContainer';
import { Bar } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface TotalMentionsProps {
  dateRange?: DateRange;
}

export const TotalMentions = ({ dateRange }: TotalMentionsProps) => {
  const params = useParams();
  const campaignId = params.id;

  // State for Total Mentions data
  const [mentionsData, setMentionsData] = useState({
    value: '12,450',
    subtitle: 'Total mentions in last 30 days',
    trend: undefined as { value: string; direction: 'up' | 'down'; description: string } | undefined,
    chartData: {
      labels: ['Jan 1', 'Jan 2', 'Jan 3', 'Jan 4', 'Jan 5'],
      datasets: [
        {
          label: 'Mentions',
          data: [3, 5, 7, 77, 0],
          backgroundColor: '#2bb684',
          borderRadius: 4,
        },
      ],
    },
    chartOptions: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false,
        },
        tooltip: {
          enabled: true,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#fff',
          bodyColor: '#fff',
          borderColor: '#333',
          borderWidth: 1,
        }
      },
      scales: {
        x: {
          display: true,
          grid: {
            display: false,
          },
          ticks: {
            color: '#4e5255',
            font: {
              size: 12,
            },
            maxRotation: 45,
          },
        },
        y: {
          display: true,
          grid: {
            color: '#f0f0f0',
          },
          ticks: {
            color: '#909498',
            font: {
              size: 12,
            },
            callback: function(value: any) {
              if (value >= 1000) {
                return (value / 1000) + 'K';
              }
              return value;
            },
          },
          min: 0,
        },
      },
    }
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const formatDateForAPI = (date: Date | null): string => {
    if (!date) return '';
    return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
  };

  const formatDateForDisplay = (dateStr: string): string => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const calculateTrendDirection = (current: number, previous: number): 'up' | 'down' => {
    return current >= previous ? 'up' : 'down';
  };

  const formatPercentage = (pct: number): string => {
    return Math.abs(pct).toFixed(2) + '%';
  };

  const getTotalMentions = async () => {
    if (!campaignId || !dateRange?.start || !dateRange?.end) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const from_date = formatDateForAPI(dateRange.start);
      const to_date = formatDateForAPI(dateRange.end);
      const response = await campaignService.getCampaignReport({
        id: campaignId,
        endpoint: 'report/total-mention',
        from_date,
        to_date
      });

      const dataResponse = response as any as TotalMentionsResponse;
      if (response) {
        const apiData = dataResponse as TotalMentionsResponse;
        // Calculate trend direction and format data
        const direction = calculateTrendDirection(apiData.curr, apiData.prev);
        const trendValue = formatPercentage(apiData.pct);

        // Format dates for chart
        const formattedDates = apiData.series.labels.map(formatDateForDisplay);

        // Update chart data
        const newChartData = {
          labels: formattedDates,
          datasets: [
            {
              label: 'Mentions',
              data: apiData.series.values,
              backgroundColor: '#2bb684',
              borderRadius: 4,
            },
          ],
        };

        setMentionsData({
          value: apiData.curr.toLocaleString(),
          subtitle: 'Total mentions in last 30 days',
          trend: {
            value: trendValue,
            direction: direction,
            description: 'compared to previous period'
          },
          chartData: newChartData,
          chartOptions: mentionsData.chartOptions // Keep existing options
        });
      }
    } catch (err) {
      console.error('Error fetching total mentions:', err);
      setError('Failed to load total mentions data');
      // Keep default data on error
      setMentionsData({
        value: '12,450',
        subtitle: 'Total mentions in last 30 days',
        trend: undefined,
        chartData: mentionsData.chartData,
        chartOptions: mentionsData.chartOptions
      });
    } finally {
      setLoading(false);
    }
  };

  // Call API when component mounts or when dateRange changes
  useEffect(() => {
    getTotalMentions();
  }, [campaignId, dateRange]);

  return (
    <>
      {!dateRange?.start || !dateRange?.end ? (
        <StatCard
          title="Total Mentions"
          subtitle="Please select a date range"
          value="--"
          icon={LineChartIcon}
          className="flex-1"
        >
          <ChartContainer height="336px">
            <div className="flex items-center justify-center h-full">
              <div className="text-[#909498] font-bodytext-sm-md">No data to display</div>
            </div>
          </ChartContainer>
        </StatCard>
      ) : loading ? (
        <StatCard
          title="Total Mentions"
          subtitle="Loading..."
          value="--"
          icon={LineChartIcon}
          className="flex-1"
        >
          <ChartContainer height="336px">
            <div className="flex items-center justify-center h-full">
              <div className="text-[#909498] font-bodytext-sm-md">Loading chart...</div>
            </div>
          </ChartContainer>
        </StatCard>
      ) : error ? (
        <StatCard
          title="Total Mentions"
          subtitle="Error loading data"
          value="--"
          icon={LineChartIcon}
          className="flex-1"
        >
          <ChartContainer height="336px">
            <div className="flex items-center justify-center h-full">
              <div className="text-red-500 font-bodytext-sm-md">{error}</div>
            </div>
          </ChartContainer>
        </StatCard>
      ) : (
        <StatCard
          title="Total Mentions"
          subtitle={mentionsData.subtitle}
          value={mentionsData.value}
          trend={mentionsData.trend}
          icon={LineChartIcon}
          className="flex-1"
        >
          <ChartContainer height="336px">
            <Bar data={mentionsData.chartData} options={mentionsData.chartOptions} />
          </ChartContainer>
        </StatCard>
      )}
    </>
  );
};
