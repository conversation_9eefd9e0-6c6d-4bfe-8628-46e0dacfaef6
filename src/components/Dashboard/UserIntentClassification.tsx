import { campaignService, UserIntentResponse } from '@/api';
import { PostIntentData, TopPostsByIntentResponse } from '@/types/campaign';
import React, { useEffect, useState } from 'react';
import { DateRange } from '@/components';
import { useParams } from 'wouter';
import { calculatePercentage, formatDateForAPI } from '@/lib/utils';
import { Card } from '@/components/ui/card';
import { Doughnut } from 'react-chartjs-2';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RenderPostCard } from '@/components/Dashboard/RenderPostCard';

interface UserIntentClassificationProps {
  dateRange?: DateRange;
}

export const UserIntentClassification = ({ dateRange }: UserIntentClassificationProps): JSX.Element => {
  const params = useParams();
  const campaignId = params.id;
  // Data for mentioned posts
  const [postsLoading, setPostsLoading] = useState(false);
  const [postsError, setPostsError] = useState<string | null>(null);
  const [tabActive, setTabActive] = useState<'PROVIDER' | 'SEEKER' | 'NEUTRAL'>('PROVIDER');
  const [providerPostsData, setProviderPostsData] = useState<PostIntentData[]>([]);
  const [seekerPostsData, setSeekerPostsData] = useState<PostIntentData[]>([]);
  const [neutralPostsData, setNeutralPostsData] = useState<PostIntentData[]>([]);
  // State for User Intent data
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalIntents, setTotalIntents] = useState<number>(0);
  const getTopPostsByIntent = async (intent: 'PROVIDER' | 'SEEKER' | 'NEUTRAL') => {
    if (!campaignId || !dateRange?.start || !dateRange?.end) {
      return;
    }

    try {
      setPostsLoading(true);
      setPostsError(null);

      const from_date = formatDateForAPI(dateRange.start);
      const to_date = formatDateForAPI(dateRange.end);

      const response = await campaignService.getCampaignReport({
        id: campaignId,
        endpoint: 'report/top-posts-by-intent',
        from_date,
        to_date,
        params: { intent }
      });
      const dataResponse = response as any as TopPostsByIntentResponse;
      if (response) {
        const apiData = dataResponse as TopPostsByIntentResponse;

        const transformedPosts: PostIntentData[] = apiData.map((post, index) => ( {
          id: post.author_id,
          author: post.author_name,
          time: formatPostTime(post.post_time),
          platform: 'Social Media',
          sentiment: post.sentiment.toLowerCase(),
          sentimentIcon: getSentimentIcon(post.sentiment),
          content: post.content,
          likes: post.engagement.like,
          comments: post.engagement.comment,
          shares: post.engagement.share,
          highlightPosition: { left: '3.5px', width: '114px' }
        } )) || [];

        // Update appropriate state based on intent
        switch (intent) {
          case 'PROVIDER':
            setProviderPostsData(transformedPosts);
            break;
          case 'SEEKER':
            setSeekerPostsData(transformedPosts);
            break;
          case 'NEUTRAL':
            setNeutralPostsData(transformedPosts);
            break;
        }
      }
    } catch (err) {
      setPostsError(`Failed to load ${intent.toLowerCase()} posts`);
    } finally {
      setPostsLoading(false);
    }
  };

  const formatPostTime = (postTime: string): string => {
    const date = new Date(postTime);
    const now = new Date();
    const diffInHours = Math.floor(( now.getTime() - date.getTime() ) / ( 1000 * 60 * 60 ));

    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }
  };

  const getSentimentIcon = (sentiment: string): string => {
    switch (sentiment.toUpperCase()) {
      case 'POSITIVE':
        return '/figmaAssets/emotion-happy-line.svg';
      case 'NEGATIVE':
        return '/figmaAssets/emotion-unhappy-line.svg';
      case 'NEUTRAL':
      default:
        return '/figmaAssets/emotion-normal-line.svg';
    }
  };

  // Chart data
  const [chartData, setChartData] = useState([
    {
      label: 'Provider',
      percentage: '48.0%',
      color: 'bg-[#7c47e6]',
      textColor: 'text-[#7c47e6]',
      rawValue: 48
    },
    {
      label: 'Seeker',
      percentage: '29.0%',
      color: 'bg-[#a37eff]',
      textColor: 'text-[#a37eff]',
      rawValue: 29
    },
    {
      label: 'Neutral',
      percentage: '23.0%',
      color: 'bg-[#adb0b4]',
      textColor: 'text-[#adb0b4]',
      rawValue: 23
    }
  ]);

  const textCenterDonutIntents = {
    id: 'textCenterDonutIntents',
    afterDatasetsDraw(chart: any, args: any, options: any) {
      const { ctx, chartArea } = chart;

      if (!chartArea) {
        return;
      }

      ctx.save();

      // Calculate center position
      const centerX = ( chartArea.left + chartArea.right ) / 2;
      const centerY = ( chartArea.top + chartArea.bottom ) / 2;

      // Draw "Total" text
      ctx.fillStyle = '#4e5255';
      ctx.font = '12px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('Total', centerX, centerY - 12);

      // Draw total value
      ctx.fillStyle = '#141416';
      ctx.font = 'bold 24px Inter, sans-serif';
      ctx.fillText(totalIntents.toLocaleString(), centerX, centerY + 8);

      ctx.restore();
    }
  };

  // User Intent Classification doughnut chart data (dynamic)
  const intentChartData = {
    labels: chartData.map(item => item.label),
    datasets: [
      {
        data: chartData.map(item => parseFloat(item.percentage.replace('%', ''))),
        backgroundColor: chartData.map(item => item.color.replace('bg-[', '').replace(']', '')),
        borderWidth: 0,
        cutout: '60%'
      }
    ]
  };

  const intentChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#333',
        borderWidth: 1,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            const dataIndex = context.dataIndex;
            const rawValue = chartData[dataIndex]?.rawValue || 0;
            const percentage = context.raw.toFixed(1);
            return `${context.label}: ${rawValue.toLocaleString()} (${percentage}%)`;
          }
        }
      }
    },
    interaction: {
      intersect: false,
      mode: 'nearest' as const
    },
    hover: {
      mode: 'nearest' as const,
      intersect: false
    },
    elements: {
      arc: {
        borderWidth: 2,
        hoverBorderWidth: 4,
        hoverBorderColor: '#fff'
      }
    }
  };

  // Call API when component mounts or when dateRange changes
  useEffect(() => {
    const getUserIntentAnalysis = async () => {
      if (!campaignId || !dateRange?.start || !dateRange?.end) {
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const from_date = formatDateForAPI(dateRange.start);
        const to_date = formatDateForAPI(dateRange.end);

        const response = await campaignService.getCampaignReport({
          id: campaignId,
          endpoint: 'report/user-intent-analysis',
          from_date,
          to_date
        });
        const dataResponse = response as any as UserIntentResponse;
        if (response) {
          const apiData = dataResponse as UserIntentResponse;

          const total = apiData.PROVIDER + apiData.SEEKER + apiData.NEUTRAL;
          setTotalIntents(total);

          // Update chart data with API response
          setChartData([
            {
              label: 'Provider',
              percentage: calculatePercentage(apiData.PROVIDER, total),
              color: 'bg-[#7c47e6]',
              textColor: 'text-[#7c47e6]',
              rawValue: apiData.PROVIDER
            },
            {
              label: 'Seeker',
              percentage: calculatePercentage(apiData.SEEKER, total),
              color: 'bg-[#a37eff]',
              textColor: 'text-[#a37eff]',
              rawValue: apiData.SEEKER
            },
            {
              label: 'Neutral',
              percentage: calculatePercentage(apiData.NEUTRAL, total),
              color: 'bg-[#adb0b4]',
              textColor: 'text-[#adb0b4]',
              rawValue: apiData.NEUTRAL
            }
          ]);
        }
      } catch (err) {
        setError('Failed to load user intent data');
      } finally {
        setLoading(false);
      }
    };
    getUserIntentAnalysis();
  }, [campaignId, dateRange]);

  // Load posts for all intents when dateRange changes
  useEffect(() => {
    if (dateRange?.start && dateRange?.end && tabActive) {
      getTopPostsByIntent(tabActive);
    }
  }, [campaignId, dateRange, tabActive]);

  return (
    <>
      {/* Right panel - User Intent Classification */}
      <Card className="flex-1 gap-6 px-6 py-4 bg-[#fdfdfd] rounded-2xl border-[#c5c8cb]">
        <div className="font-bodytext-md-md text-[#4e5255] text-[length:var(--bodytext-md-md-font-size)] tracking-[var(--bodytext-md-md-letter-spacing)] leading-[var(--bodytext-md-md-line-height)]">
          User Intent Classification
        </div>

        <div className="flex flex-col items-center justify-center gap-6 px-3 py-0 w-full rounded-2xl">
          {!dateRange?.start || !dateRange?.end ? (
            <div className="flex flex-col items-center justify-center gap-4 h-[172px]">
              <div className="text-[#909498] font-bodytext-sm-md">Please select a date range</div>
            </div>
          ) : loading ? (
            <div className="flex flex-col items-center justify-center gap-4 h-[172px]">
              <div className="text-[#909498] font-bodytext-sm-md">Loading user intent data...</div>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center gap-4 h-[172px]">
              <div className="text-red-500 font-bodytext-sm-md">{error}</div>
            </div>
          ) : (
            <>
              {/* Interactive Intent Classification Doughnut Chart */}
              <div className="relative w-[172px] h-[172px]">
                <Doughnut
                  key={totalIntents}
                  data={intentChartData}
                  options={intentChartOptions}
                  plugins={[textCenterDonutIntents]}
                />
              </div>

              {/* Chart legend */}
              <div className="flex items-center justify-center gap-6 w-full">
                {chartData.map((item, index) => (
                  <div key={index} className="flex items-center gap-1">
                    <div className={`w-3 h-3 rounded ${item.color}`} />
                    <div className="font-bodytext-xs-md text-[#6b7183] text-[length:var(--bodytext-xs-md-font-size)] tracking-[var(--bodytext-xs-md-letter-spacing)] leading-[var(--bodytext-xs-md-line-height)]">
                      {item.label}
                    </div>
                    <div
                      className={`font-body text-sm-semibold ${item.textColor} text-[length:var(--bodytext-sm-semibold-font-size)] tracking-[var(--bodytext-sm-semibold-letter-spacing)] leading-[var(--bodytext-sm-semibold-line-height)]`}
                    >
                      {item.percentage}
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>

        {/* Intent tabs */}
        <Tabs
          defaultValue="PROVIDER"
          className="w-full"
          onValueChange={(value) => setTabActive(value as 'PROVIDER' | 'SEEKER' | 'NEUTRAL')}
        >
          <TabsList className="flex h-10 gap-2 w-full border-b border-[#c5c8cb] bg-transparent p-0">
            <TabsTrigger
              value="PROVIDER"
              className="flex-1 rounded-none border-b-2 border-[#7c47e6] data-[state=active]:border-[#7c47e6] data-[state=active]:text-[#7c47e6] data-[state=inactive]:border-[#c5c8cb] data-[state=inactive]:text-[#4e5255] pb-2 pt-0 px-1 bg-transparent"
            >
              <div className="font-bodytext-sm-md px-2 py-1">Provider</div>
            </TabsTrigger>
            <TabsTrigger
              value="SEEKER"
              className="flex-1 rounded-none border-b-2 border-[#c5c8cb] data-[state=active]:border-[#7c47e6] data-[state=active]:text-[#7c47e6] data-[state=inactive]:border-[#c5c8cb] data-[state=inactive]:text-[#4e5255] pb-2 pt-0 px-1 bg-transparent"
            >
              <div className="font-bodytext-sm-md px-2 py-1">Seeker</div>
            </TabsTrigger>
            <TabsTrigger
              value="NEUTRAL"
              className="flex-1 rounded-none border-b-2 border-[#c5c8cb] data-[state=active]:border-[#7c47e6] data-[state=active]:text-[#7c47e6] data-[state=inactive]:border-[#c5c8cb] data-[state=inactive]:text-[#4e5255] pb-2 pt-0 px-1 bg-transparent"
            >
              <div className="font-bodytext-sm-md px-2 py-1">Neutral</div>
            </TabsTrigger>
          </TabsList>

          <TabsContent
            value="PROVIDER"
            className="mt-4 space-y-4 overflow-hidden h-[800px] pr-3 overflow-y-auto"
          >
            {!dateRange?.start || !dateRange?.end ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-[#909498] font-bodytext-sm-md">Please select a date range to view posts</div>
              </div>
            ) : postsLoading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-[#909498] font-bodytext-sm-md">Loading provider posts...</div>
              </div>
            ) : postsError ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-red-500 font-bodytext-sm-md">{postsError}</div>
              </div>
            ) : (
              providerPostsData.map((post) => <RenderPostCard post={post} />)
            )}
          </TabsContent>

          <TabsContent
            value="SEEKER"
            className="mt-4 space-y-4 overflow-hidden h-[800px] pr-3 overflow-y-auto"
          >
            {!dateRange?.start || !dateRange?.end ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-[#909498] font-bodytext-sm-md">Please select a date range to view posts</div>
              </div>
            ) : postsLoading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-[#909498] font-bodytext-sm-md">Loading seeker posts...</div>
              </div>
            ) : postsError ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-red-500 font-bodytext-sm-md">{postsError}</div>
              </div>
            ) : (
              seekerPostsData.map((post) => <RenderPostCard post={post} />)
            )}
          </TabsContent>

          <TabsContent
            value="NEUTRAL"
            className="mt-4 space-y-4 overflow-hidden h-[800px] pr-3 overflow-y-auto"
          >
            {!dateRange?.start || !dateRange?.end ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-[#909498] font-bodytext-sm-md">Please select a date range to view posts</div>
              </div>
            ) : postsLoading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-[#909498] font-bodytext-sm-md">Loading neutral posts...</div>
              </div>
            ) : postsError ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-red-500 font-bodytext-sm-md">{postsError}</div>
              </div>
            ) : (
              neutralPostsData.map((post) => <RenderPostCard post={post} />)
            )}
          </TabsContent>
        </Tabs>
      </Card>
    </>
  );
};
