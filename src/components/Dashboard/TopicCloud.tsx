import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { WordCloudComponent } from '@/components/ui/word-cloud';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'wouter';
import { campaignService } from '@/api';
import { TopicCloudResponse } from '@/types/campaign';
import { DateRange } from '@/components/molecules/DateRangePicker';

// Constants for font size and weight calculation
const MAX_FONT_SIZE = 32;
const MIN_FONT_SIZE = 12;
const MAX_FONT_WEIGHT = 700;
const MIN_FONT_WEIGHT = 400;
const MAX_WORDS = 100;

interface Word {
  text: string;
  value: number;
}

interface TopicCloudProps {
  dateRange?: DateRange;
}

export const TopicCloud = ({ dateRange }: TopicCloudProps) => {
  const params = useParams();
  const campaignId = params.id;

  const [words, setWords] = useState<Word[]>([
    { text: "", value: 0 },
  ]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Sort words by value and limit to MAX_WORDS
  const sortedWords = useMemo(() => {
    if (!words || words.length === 0) {
      return [];
    }
    return words.sort((a, b) => b.value - a.value).slice(0, MAX_WORDS);
  }, [words]);

  // Calculate min and max occurrences for normalization
  const [minOccurrences, maxOccurrences] = useMemo(() => {
    if (sortedWords.length === 0) {
      return [0, 1];
    }
    const values = sortedWords.map((w) => w.value);
    const min = Math.min(...values);
    const max = Math.max(...values);
    return [min, max];
  }, [sortedWords]);

  // Calculate font size based on word frequency
  const calculateFontSize = useCallback(
    (word: Word) => {
      if (maxOccurrences === minOccurrences) {
        return MIN_FONT_SIZE;
      }

      const normalizedValue =
        (word.value - minOccurrences) / (maxOccurrences - minOccurrences);
      const fontSize =
        MIN_FONT_SIZE + normalizedValue * (MAX_FONT_SIZE - MIN_FONT_SIZE);
      return Math.round(fontSize);
    },
    [maxOccurrences, minOccurrences]
  );

  // Calculate font weight based on word frequency
  const calculateFontWeight = useCallback((word: Word) => {
    if (maxOccurrences === minOccurrences) {
      return MIN_FONT_WEIGHT;
    }

    const normalizedValue =
      (word.value - minOccurrences) / (maxOccurrences - minOccurrences);
    const fontWeight =
      MIN_FONT_WEIGHT +
      normalizedValue * (MAX_FONT_WEIGHT - MIN_FONT_WEIGHT);
    return Math.round(fontWeight);
  }, [maxOccurrences, minOccurrences]);

  const formatDateForAPI = (date: Date | null): string => {
    if (!date) return '';
    return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
  };

  const getCampaignReport = async () => {
    if (!campaignId || !dateRange?.start || !dateRange?.end) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const from_date = formatDateForAPI(dateRange.start);
      const to_date = formatDateForAPI(dateRange.end);

      const response = await campaignService.getCampaignReport({
        id: campaignId,
        endpoint: 'report/relevant-keywords',
        from_date,
        to_date
      });

      const dataResponse = response as any as TopicCloudResponse;
      if (dataResponse && dataResponse.tokens) {
        const topicCloudData = dataResponse as TopicCloudResponse;
        // Convert API response to word cloud format
        const convertedData = topicCloudData.tokens.map(token => ({
          text: token.text,
          value: token.weight
        }));
        setWords(convertedData);
      }
    } catch (err) {
      console.error('Error fetching campaign report:', err);
      setError('Failed to load keyword data');
    } finally {
      setLoading(false);
    }
  };

  // Call API when component mounts or when dateRange changes
  useEffect(() => {
    getCampaignReport();
  }, [campaignId, dateRange]); // Re-run when campaignId or dateRange changes

  return (
    <>
      {/* Relevant Keyword Cloud Card */}
      <Card className="flex flex-col items-start gap-6 px-10 py-6 flex-1 bg-[#fdfdfd] rounded-[26px] border border-solid border-[#c5c8cb] h-[540px]">
        <CardHeader className="flex items-start justify-between p-0 pb-2 w-full border-b border-[#c5c8cb]">
          <div className="inline-flex flex-col items-start justify-center gap-1">
            <h3 className="font-bodytext-md-md text-[#4e5255]">
              Relevant Keyword Cloud
            </h3>
          </div>
        </CardHeader>
        <CardContent className="p-0 flex flex-col items-center justify-center gap-2.5 w-full h-full">
          <div className="flex justify-center items-center w-full h-full">
            {!dateRange?.start || !dateRange?.end ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-[#909498] font-bodytext-sm-md">Please select a date range to view keywords</div>
              </div>
            ) : loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-[#909498] font-bodytext-sm-md">Loading keywords...</div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-red-500 font-bodytext-sm-md">{error}</div>
              </div>
            ) : (
              <WordCloudComponent
                data={sortedWords}
                width={600}
                height={245}
                fontSizeMapper={calculateFontSize}
                fontWeightMapper={calculateFontWeight}
                rotate={() => 0} // Keep words horizontal for better readability
                className="word-cloud-overview"
              />
            )}
          </div>
        </CardContent>
      </Card>
    </>
  );
};
