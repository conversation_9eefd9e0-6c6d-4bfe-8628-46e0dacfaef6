import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Text } from '@/components/atoms/Text';
import { Icon } from '@/components/atoms/Icon';
import { Metric } from '@/components/atoms/Metric';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface StatCardProps {
  title: string;
  subtitle?: string;
  value: string | number;
  trend?: {
    value: string;
    direction: 'up' | 'down';
    description?: string;
  };
  icon?: LucideIcon;
  children?: React.ReactNode;
  className?: string;
}

export const StatCard: React.FC<StatCardProps> = ({
  title,
  subtitle,
  value,
  trend,
  icon,
  children,
  className
}) => {
  return (
    <Card className={cn('border border-[#d9dbde] rounded-[26px] bg-[#fdfdfd]', className)}>
      <CardContent className="p-6 flex flex-col gap-4">
        <div className="flex justify-between items-start w-full">
          <div className="flex flex-col gap-1">
            <Text variant="body-sm" color="secondary">
              {title}
            </Text>
            {subtitle && (
              <Text variant="body-sm" color="muted">
                {subtitle}
              </Text>
            )}
          </div>
          {icon && <Icon icon={icon} size="lg" />}
        </div>

        <Metric value={value} trend={trend} />

        {children}
      </CardContent>
    </Card>
  );
};
