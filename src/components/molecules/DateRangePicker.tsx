import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { CalendarIcon } from 'lucide-react';
import { Calendar } from './Calendar';
import { DateInput } from '@/components/atoms/DateInput';
import { cn } from '@/lib/utils';

export interface DateRange {
  start: Date | null;
  end: Date | null;
}

export interface DateRangePickerProps {
  value?: DateRange;
  onChange?: (range: DateRange) => void;
  placeholder?: string;
  className?: string;
}

const formatDate = (date: Date | null): string => {
  if (!date) return '';
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const year = date.getFullYear();
  return `${month}/${day}/${year}`;
};

const getDisplayText = (range: DateRange, placeholder: string): string => {
  if (!range.start && !range.end) return placeholder;
  if (range.start && !range.end) return `${formatDate(range.start)} - Select end date`;
  if (range.start && range.end) {
    const diffDays = Math.ceil((range.end.getTime() - range.start.getTime()) / (1000 * 60 * 60 * 24));
    return `Last ${diffDays + 1} days`;
  }
  return placeholder;
};

export const DateRangePicker: React.FC<DateRangePickerProps> = ({
  value = { start: null, end: null },
  onChange,
  placeholder = "Last 30 days",
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [tempRange, setTempRange] = useState<DateRange>(value);

  const handleDateSelect = (date: Date) => {
    if (!tempRange.start || (tempRange.start && tempRange.end)) {
      // Start new selection
      setTempRange({ start: date, end: null });
    } else if (tempRange.start && !tempRange.end) {
      // Complete the range
      if (date >= tempRange.start) {
        setTempRange({ start: tempRange.start, end: date });
      } else {
        setTempRange({ start: date, end: tempRange.start });
      }
    }
  };

  const handleApply = () => {
    onChange?.(tempRange);
    setIsOpen(false);
  };

  const handleCancel = () => {
    setTempRange(value);
    setIsOpen(false);
  };

  const displayText = getDisplayText(value, placeholder);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "h-10 gap-2 px-2 py-1 rounded-2xl border-[#c5c8cb] bg-[#fdfdfd] justify-start",
            className
          )}
        >
          <CalendarIcon className="w-4 h-4" />
          <span className="font-bodytext-sm-md text-[#141416]">
            {displayText}
          </span>
          <CalendarIcon className="w-4 h-4 ml-auto" />
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-auto p-0" align="end">
        <Card className="border-[#d9dbde] shadow-soft-shadow-sm p-3 w-[370px]">
          <CardContent className="p-0 space-y-4">
            {/* Date Range Inputs */}
            <div className="flex items-center gap-2">
              <div className="flex-1">
                <DateInput 
                  value={formatDate(tempRange.start)}
                  placeholder="Start date"
                />
              </div>
              <span className="font-bodytext-sm-md text-[#909498]">-</span>
              <div className="flex-1">
                <DateInput 
                  value={formatDate(tempRange.end)}
                  placeholder="End date"
                />
              </div>
            </div>

            <Separator className="my-2" />

            {/* Calendar */}
            <Calendar
              selectedRange={tempRange}
              onDateSelect={handleDateSelect}
            />

            <Separator className="my-2" />

            {/* Action Buttons */}
            <div className="flex items-center justify-end gap-3">
              <Button
                variant="outline"
                className="flex-1 rounded-xl border-[#c5c8cb]"
                onClick={handleCancel}
              >
                <span className="font-bodytext-sm-md text-[#4e5255]">
                  Cancel
                </span>
              </Button>
              <Button 
                className="flex-1 bg-[#7c47e6] rounded-xl hover:bg-[#6a3bc4]"
                onClick={handleApply}
                disabled={!tempRange.start || !tempRange.end}
              >
                <span className="font-bodytext-sm-md text-[#f4f0ff]">
                  Apply
                </span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  );
};
