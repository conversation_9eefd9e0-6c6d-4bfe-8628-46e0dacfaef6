# DateRangePicker Component

## Overview
A comprehensive date range picker component that allows users to select start and end dates with a calendar interface. Returns dates in MM/DD/YYYY format.

## Features
- ✅ Interactive calendar with month/year navigation
- ✅ Date range selection (start and end dates)
- ✅ Visual range highlighting
- ✅ Date input fields showing selected dates
- ✅ Apply/Cancel functionality
- ✅ Returns dates in MM/DD/YYYY format
- ✅ Responsive design
- ✅ Keyboard accessible

## Components Structure

### Atoms
- **DateInput**: Display component for showing formatted dates
  ```tsx
  <DateInput value="05/11/2025" placeholder="MM/DD/YYYY" />
  ```

### Molecules
- **Calendar**: Interactive calendar with date selection
  ```tsx
  <Calendar
    selectedRange={{ start: startDate, end: endDate }}
    onDateSelect={handleDateSelect}
  />
  ```

- **DateRangePicker**: Main component combining all functionality
  ```tsx
  <DateRangePicker
    value={dateRange}
    onChange={handleDateRangeChange}
    placeholder="Last 30 days"
  />
  ```

## Usage Example

```tsx
import React, { useState } from 'react';
import { DateRangePicker, DateRange } from '@/components/molecules/DateRangePicker';

const MyComponent = () => {
  const [dateRange, setDateRange] = useState<DateRange>({
    start: new Date(2025, 4, 11), // May 11, 2025
    end: new Date(2025, 5, 9)     // June 9, 2025
  });

  const handleDateRangeChange = (range: DateRange) => {
    setDateRange(range);
    console.log('Selected dates:', {
      start: range.start?.toLocaleDateString('en-US'), // "05/11/2025"
      end: range.end?.toLocaleDateString('en-US')       // "06/09/2025"
    });
  };

  return (
    <DateRangePicker
      value={dateRange}
      onChange={handleDateRangeChange}
      placeholder="Select date range"
    />
  );
};
```

## Props

### DateRangePicker Props
```typescript
interface DateRangePickerProps {
  value?: DateRange;           // Current selected date range
  onChange?: (range: DateRange) => void; // Callback when range changes
  placeholder?: string;        // Placeholder text (default: "Last 30 days")
  className?: string;          // Additional CSS classes
}

interface DateRange {
  start: Date | null;          // Start date
  end: Date | null;            // End date
}
```

### Calendar Props
```typescript
interface CalendarProps {
  selectedRange?: DateRange;   // Currently selected range
  onDateSelect?: (date: Date) => void; // Callback when date is clicked
  className?: string;          // Additional CSS classes
}
```

### DateInput Props
```typescript
interface DateInputProps {
  value: string;               // Formatted date string
  placeholder?: string;        // Placeholder text
  className?: string;          // Additional CSS classes
  readOnly?: boolean;          // Whether input is read-only (default: true)
}
```

## Date Format
- **Input**: JavaScript Date objects
- **Output**: MM/DD/YYYY format strings
- **Display**: Formatted according to locale

## Styling
- Uses Tailwind CSS classes
- Consistent with design system colors
- Purple theme (#7c47e6) for selected dates
- Light purple (#e3daff) for range highlighting
- Responsive design with proper spacing

## Behavior
1. **Single Click**: Starts new selection (sets start date)
2. **Second Click**: Completes range (sets end date)
3. **Third Click**: Starts new selection again
4. **Apply Button**: Confirms selection and closes popover
5. **Cancel Button**: Reverts to previous selection and closes popover

## Integration
The DateRangePicker has been integrated into the CampaignDetail page, replacing the previous hardcoded calendar implementation. It provides:

- Better user experience with interactive date selection
- Proper state management
- Reusable component for other parts of the application
- Consistent styling with the design system

## Example Output
When a user selects May 11, 2025 to June 9, 2025:
```javascript
{
  start: "05/11/2025",
  end: "06/09/2025"
}
```
