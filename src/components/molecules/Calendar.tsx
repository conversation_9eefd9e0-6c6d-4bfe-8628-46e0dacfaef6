import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeftIcon, ChevronRightIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface CalendarDate {
  day: number;
  month: number;
  year: number;
  isCurrentMonth: boolean;
  isSelected?: boolean;
  isInRange?: boolean;
  isRangeStart?: boolean;
  isRangeEnd?: boolean;
}

export interface CalendarProps {
  selectedRange?: {
    start: Date | null;
    end: Date | null;
  };
  onDateSelect?: (date: Date) => void;
  className?: string;
}

const MONTHS = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

const WEEKDAYS = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

export const Calendar: React.FC<CalendarProps> = ({
  selectedRange,
  onDateSelect,
  className
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());

  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  const goToPreviousMonth = () => {
    setCurrentDate(new Date(currentYear, currentMonth - 1, 1));
  };

  const goToNextMonth = () => {
    setCurrentDate(new Date(currentYear, currentMonth + 1, 1));
  };

  const generateCalendarDays = (): CalendarDate[] => {
    const firstDayOfMonth = new Date(currentYear, currentMonth, 1);
    const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);
    const firstDayWeekday = (firstDayOfMonth.getDay() + 6) % 7; // Convert Sunday=0 to Monday=0
    
    const days: CalendarDate[] = [];

    // Previous month days
    const prevMonth = new Date(currentYear, currentMonth - 1, 0);
    for (let i = firstDayWeekday - 1; i >= 0; i--) {
      const day = prevMonth.getDate() - i;
      days.push({
        day,
        month: currentMonth - 1,
        year: currentMonth === 0 ? currentYear - 1 : currentYear,
        isCurrentMonth: false
      });
    }

    // Current month days
    for (let day = 1; day <= lastDayOfMonth.getDate(); day++) {
      const date = new Date(currentYear, currentMonth, day);
      const isSelected = (selectedRange?.start &&
        date.toDateString() === selectedRange.start.toDateString()) ||
        (selectedRange?.end &&
        date.toDateString() === selectedRange.end.toDateString());
      
      const isInRange = selectedRange?.start && selectedRange?.end &&
        date >= selectedRange.start && date <= selectedRange.end;

      const isRangeStart = selectedRange?.start && 
        date.toDateString() === selectedRange.start.toDateString();
      
      const isRangeEnd = selectedRange?.end && 
        date.toDateString() === selectedRange.end.toDateString();

      days.push({
        day,
        month: currentMonth,
        year: currentYear,
        isCurrentMonth: true,
        isSelected: isSelected || undefined,
        isInRange: isInRange || undefined,
        isRangeStart: isRangeStart || undefined,
        isRangeEnd: isRangeEnd || undefined
      });
    }

    // Next month days to fill the grid
    const remainingDays = 42 - days.length; // 6 rows × 7 days
    for (let day = 1; day <= remainingDays; day++) {
      days.push({
        day,
        month: currentMonth + 1,
        year: currentMonth === 11 ? currentYear + 1 : currentYear,
        isCurrentMonth: false
      });
    }

    return days;
  };

  const handleDateClick = (calendarDate: CalendarDate) => {
    const date = new Date(calendarDate.year, calendarDate.month, calendarDate.day);
    onDateSelect?.(date);
  };

  const calendarDays = generateCalendarDays();

  return (
    <div className={cn('space-y-4', className)}>
      {/* Month/Year Navigation */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          size="icon"
          className="rounded-xl border-[#c5c8cb]"
          onClick={goToPreviousMonth}
        >
          <ChevronLeftIcon className="w-4 h-4" />
        </Button>
        
        <div className="flex items-center gap-1 font-bodytext-sm-md text-[#141416]">
          <span>{MONTHS[currentMonth]},</span>
          <span>{currentYear}</span>
        </div>
        
        <Button
          variant="outline"
          size="icon"
          className="rounded-xl border-[#c5c8cb]"
          onClick={goToNextMonth}
        >
          <ChevronRightIcon className="w-4 h-4" />
        </Button>
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-0">
        {/* Weekday Headers */}
        {WEEKDAYS.map((day, index) => (
          <div
            key={`weekday-${index}`}
            className="flex items-center justify-center h-[42px] px-2 py-2.5"
          >
            <span className="font-bodytext-sm-md text-[#909498]">
              {day}
            </span>
          </div>
        ))}

        {/* Calendar Days */}
        {calendarDays.map((calendarDate, index) => {
          let className = "flex flex-col items-center justify-center h-[42px] px-4 py-2 cursor-pointer hover:bg-gray-100";
          let textColor = "text-[#4e5255]";

          if (!calendarDate.isCurrentMonth) {
            textColor = "text-[#c5c8cb]";
          }

          if (calendarDate.isInRange && !calendarDate.isSelected) {
            className += " bg-[#e3daff]";
            textColor = "text-[#7c47e6]";
          }

          if (calendarDate.isSelected) {
            className += " relative";
            textColor = "text-[#f4f0ff]";
          }

          if (calendarDate.isRangeStart) {
            className += " rounded-l-2xl";
          }

          if (calendarDate.isRangeEnd) {
            className += " rounded-r-2xl";
          }

          return (
            <div 
              key={`day-${index}`} 
              className={className}
              onClick={() => handleDateClick(calendarDate)}
            >
              {calendarDate.isSelected && (
                <div className="absolute w-full h-full top-0 left-0 bg-[#7c47e6] rounded-2xl" />
              )}
              <span className={`relative ${textColor} font-bodytext-sm-md`}>
                {calendarDate.day}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
};
