import React, { useState, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import WordCloud from 'react-d3-cloud';

interface WordData {
  text: string;
  value: number;
}

interface WordCloudComponentProps {
  data: WordData[];
  width?: number;
  height?: number;
  fontSizeMapper?: (word: WordData) => number;
  fontWeightMapper?: (word: WordData) => number;
  rotate?: (word: WordData) => number;
  className?: string;
}

export const WordCloudComponent: React.FC<WordCloudComponentProps> = ({
  data,
  width = 400,
  height = 200,
  fontSizeMapper = (word: WordData) => Math.log2(word.value) * 5 + 10,
  fontWeightMapper = () => 600,
  rotate = () => (Math.random() - 0.5) * 60,
  className = "",
}) => {
  const [tooltip, setTooltip] = useState<{
    visible: boolean;
    x: number;
    y: number;
    word: string;
    value: number;
  }>({
    visible: false,
    x: 0,
    y: 0,
    word: '',
    value: 0
  });

  const [hideTimeout, setHideTimeout] = useState<any>(null);
  const [isOverWord, setIsOverWord] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (hideTimeout) {
        clearTimeout(hideTimeout);
      }
    };
  }, [hideTimeout]);

  const handleWordMouseOver = useCallback((event: any, word: any) => {
    if (hideTimeout) {
      clearTimeout(hideTimeout);
      setHideTimeout(null);
    }

    setIsOverWord(true);
    setShouldRender(true);

    setTooltip({
      visible: true,
      x: event.clientX,
      y: event.clientY - 60,
      word: word.text,
      value: word.value
    });
  }, [hideTimeout]);

  const handleWordMouseOut = useCallback(() => {
    setIsOverWord(false);
  }, []);

  const fillColor = (word: WordData) => {
    const colors = ['#7c47e6', '#4f9ef0', '#2bb684', '#f84242', '#a37eff', '#adb0b4'];
    // Use a more deterministic color selection based on word text hash
    const hash = word.text.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    return colors[Math.abs(hash) % colors.length];
  };

  if (!data || data.length === 0) {
    return (
      <div
        className={`word-cloud-container ${className} flex items-center justify-center text-slate-500 text-sm`}
        data-testid="word-cloud"
        style={{
          width: `${width}px`,
          height: `${height}px`,
        }}
      >
        No data available
      </div>
    );
  }

  return (
    <div
      className={`word-cloud-container ${className} relative overflow-hidden flex items-center justify-center`}
      data-testid="word-cloud"
      style={{
        width: `${width}px`,
        height: `${height}px`,
      }}
    >
      <div
        className="w-full h-full relative overflow-hidden [&_text]:cursor-pointer"
        onMouseLeave={() => {
          const timeout = setTimeout(() => {
            setTooltip(prev => ({ ...prev, visible: false }));
            setTimeout(() => setShouldRender(false), 200);
          }, 500);
          setHideTimeout(timeout);
        }}
      >
        <div
          onMouseMove={(e) => {
            if (tooltip.visible) {
              setTooltip(prev => ({
                ...prev,
                x: e.clientX,
                y: e.clientY - 70
              }));

              const target = e.target as Element;
              const isCurrentlyOverText = target.tagName === 'text' || target.closest('text');

              if (isCurrentlyOverText) {
                setIsOverWord(true);
                if (hideTimeout) {
                  clearTimeout(hideTimeout);
                  setHideTimeout(null);
                }
              } else if (isOverWord) {
                setIsOverWord(false);
                if (!hideTimeout) {
                  const timeout = setTimeout(() => {
                    setTooltip(prev => ({ ...prev, visible: false }));
                    setTimeout(() => setShouldRender(false), 200);
                  }, 150);
                  setHideTimeout(timeout);
                }
              }
            }
          }}
        >
          <WordCloud
            data={data}
            width={width}
            height={height - 20}
            font="Inter, sans-serif"
            fontStyle="normal"
            fontWeight={fontWeightMapper}
            fontSize={fontSizeMapper}
            spiral="archimedean"
            rotate={0}
            padding={2}
            random={() => 0.5}
            fill={fillColor}
            onWordMouseOver={handleWordMouseOver}
            onWordMouseOut={handleWordMouseOut}
          />
        </div>
      </div>

      {/* Tooltip using Portal with smooth animations */}
      {shouldRender && createPortal(
        <div
          className={`fixed pointer-events-none z-[9999] whitespace-nowrap max-w-full bg-black/90 text-white px-3 py-2 rounded-md text-sm font-medium shadow-lg border border-white/10 transition-all duration-200 ease-out ${
            tooltip.visible
              ? 'opacity-100 scale-100 translate-y-0'
              : 'opacity-0 scale-95 translate-y-1 pointer-events-none'
          }`}
          style={{
            left: Math.min(tooltip.x, window.innerWidth - 100),
            top: Math.max(tooltip.y, 10),
            transform: 'translateX(-50%)',
          }}
        >
          <div className="font-semibold">{tooltip.word}</div>
          <div className="text-xs opacity-90 mt-0.5">
            Count: {tooltip.value.toLocaleString()}
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};
