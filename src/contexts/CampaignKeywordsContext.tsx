import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { useParams } from 'wouter';
import { campaignService } from '@/api';
import { CampaignDetailResponse } from '@/types/campaign';

interface CampaignKeywordsContextType {
  keywords: string[];
  mentionTags: string[];
  keywordTags: string[];
  allTagKeyword: string[];
  campaignDetail: CampaignDetailResponse | null;
  loading: boolean;
  error: string | null;
  refreshCampaignDetail: () => void;
  highlightKeywords: (text: string) => React.ReactNode;
}

const CampaignKeywordsContext = createContext<CampaignKeywordsContextType | undefined>(undefined);

interface CampaignKeywordsProviderProps {
  children: ReactNode;
}

export const CampaignKeywordsProvider: React.FC<CampaignKeywordsProviderProps> = ({ children }) => {
  const params = useParams();
  const campaignId = params.id;

  const [keywords, setKeywords] = useState<string[]>([]);
  const [mentionTags, setMentionTags] = useState<string[]>([]);
  const [keywordTags, setKeywordTags] = useState<string[]>([]);
  const [allTagKeyword, setAllTagKeyword] = useState<string[]>([]);
  const [campaignDetail, setCampaignDetail] = useState<CampaignDetailResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getCampaignDetail = async () => {
    if (!campaignId) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response: CampaignDetailResponse = await campaignService.getCampaign(campaignId) as any | CampaignDetailResponse;


      if (response && response.tags) {
        const campaignData = response as CampaignDetailResponse;

        // Store full campaign detail
        setCampaignDetail(campaignData);

        // Separate tags by type (same logic as TagsSection)
        const mentions: string[] = [];
        const keywords: string[] = [];
        const allKeywordsForHighlighting: string[] = [];

        campaignData.tags.forEach(tag => {
          const value = tag.tag_value;

          if (tag.tag_type === 'MENTION') {
            // Add # prefix for mentions if not present
            const mentionValue = value.startsWith('#') ? value : `#${value}`;
            mentions.push(mentionValue);

            // For highlighting, use clean value without #
            const cleanValue = value.startsWith('#') ? value.substring(1) : value;
            if (cleanValue.trim()) {
              allKeywordsForHighlighting.push(cleanValue.toLowerCase().trim());
            }
          } else if (tag.tag_type === 'KEYWORD') {
            keywords.push(value);

            // For highlighting, use the value as-is
            if (value.trim()) {
              allKeywordsForHighlighting.push(value.toLowerCase().trim());
            }
          }
        });
        setAllTagKeyword(campaignData.tags.map((tag)=> tag.tag_value));
        setMentionTags(mentions);
        setKeywordTags(keywords);

        // @ts-ignore
        const uniqueKeywords = [...new Set(allKeywordsForHighlighting)].sort((a, b) => b.length - a.length);
        setKeywords(uniqueKeywords);
      } else {
        setCampaignDetail(null);
        setMentionTags([]);
        setKeywordTags([]);
        setKeywords([]);
      }
    } catch (err) {
      setError('Failed to load campaign detail');
      setCampaignDetail(null);
      setMentionTags([]);
      setKeywordTags([]);
      setKeywords([]);
    } finally {
      setLoading(false);
    }
  };

  const highlightKeywords = (text: string): React.ReactNode => {
    if (!text || keywords.length === 0) {
      return text;
    }

    // Sort keywords by length (descending) to match longer keywords first
    const sortedKeywords = [...keywords].sort((a, b) => b.length - a.length);
    
    // Escape special regex characters in keywords
    const escapedKeywords = sortedKeywords.map(keyword => 
      keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    );
    
    if (escapedKeywords.length === 0) {
      return text;
    }

    const pattern = new RegExp(`\\b(${escapedKeywords.join('|')})\\b`, 'gi');
    
    const parts = text.split(pattern);
    
    return parts.map((part, index) => {
      const isKeyword = keywords.some(keyword =>
        keyword.toLowerCase() === part.toLowerCase()
      );
      
      if (isKeyword) {
        return (
          <span
            key={index}
            style={{
              color: '#1e40af',
              fontWeight: 600,
              padding: '1px'
            }}
          >
            {part}
          </span>
        );
      }
      
      return part;
    });
  };

  const refreshCampaignDetail = () => {
    getCampaignDetail();
  };

  // Load campaign detail when component mounts or campaignId changes
  useEffect(() => {
    getCampaignDetail();
  }, [campaignId]);

  const value: CampaignKeywordsContextType = {
    keywords,
    mentionTags,
    keywordTags,
    allTagKeyword,
    campaignDetail,
    loading,
    error,
    refreshCampaignDetail,
    highlightKeywords
  };

  return (
    <CampaignKeywordsContext.Provider value={value}>
      {children}
    </CampaignKeywordsContext.Provider>
  );
};

export const useCampaignKeywords = (): CampaignKeywordsContextType => {
  const context = useContext(CampaignKeywordsContext);
  if (context === undefined) {
    throw new Error('useCampaignKeywords must be used within a CampaignKeywordsProvider');
  }
  return context;
};
