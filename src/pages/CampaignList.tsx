import React, { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { Navigation } from '@/components/organisms/Navigation';
import { CampaignGrid, type CampaignGridItem } from '@/components/organisms/CampaignGrid';
import LoadingIndicator from '@/components/atoms/LoadingSpinner';
import { campaignService } from '@/api';
import type { Campaign } from '@/types/campaign';

export const CampaignList = (): JSX.Element => {
  const [, setLocation] = useLocation();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch campaigns from API
  useEffect(() => {
    const fetchCampaigns = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await campaignService.getCampaigns() as any as Campaign[];
        setCampaigns(response);
      } catch (err) {
        console.error('Error fetching campaigns:', err);
        setError('Failed to load campaigns');
      } finally {
        setLoading(false);
      }
    };

    fetchCampaigns();
  }, []);

  const handlePreview = (campaignId: string) => {
    setLocation(`/campaign/${campaignId}`);
  };

  // Transform API data to match CampaignGrid expected format
  const transformedCampaigns: CampaignGridItem[] = campaigns.map(campaign => ( {
    id: campaign.id,
    title: campaign.name,
    status: 'RUNNING' as const, // Default status since API doesn't provide it
    keywords: campaign.tags.map(tag => tag.tag_value),
    moreKeywordsCount: Math.max(0, campaign.tags.length - 6),
    createdDate: new Date(campaign?.created_at ?? '').toLocaleDateString('en-US', {
      month: 'numeric',
      day: 'numeric',
      year: 'numeric'
    })
  } ));

  if (loading) {
    return (
      <div className="flex flex-col w-full items-start relative bg-[#f9fafb] min-h-screen">
        <Navigation
          title="Campaign Management"
          user={{
            name: 'Trịnh Hoàng Tuấn',
            avatar: '/TrinhHoangTuan.jpg',
            fallback: 'DS'
          }}
        />
        <div className="flex flex-col w-full items-center justify-center py-20">
          <LoadingIndicator size="lg" text="Loading Campaigns" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col w-full items-start relative bg-[#f9fafb] min-h-screen">
        <Navigation
          title="Campaign Management"
          user={{
            name: 'Trịnh Hoàng Tuấn',
            avatar: '/TrinhHoangTuan.jpg',
            fallback: 'DS'
          }}
        />
        <div className="flex flex-col w-full items-center justify-center py-20">
          <div className="text-lg text-red-600">{error}</div>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full items-start relative bg-[#f9fafb] min-h-screen">
      <Navigation
        title="Campaign Management"
        user={{
          name: 'Trịnh Hoàng Tuấn',
          avatar: '/TrinhHoangTuan.jpg',
          fallback: 'DS'
        }}
      />

      <div className="flex flex-col w-full items-start gap-6 px-8 md:px-[142px] py-8 relative">
        <CampaignGrid
          campaigns={transformedCampaigns}
          totalCount={campaigns.length}
          onPreview={handlePreview}
        />
      </div>
    </div>
  );
};
