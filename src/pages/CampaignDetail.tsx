import { ArrowLeftIcon } from 'lucide-react';
import React, { useState } from 'react';
import { useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { DateRange, DateRangePicker } from '@/components/molecules/DateRangePicker';
import { AnalyticsSection } from './sections/AnalyticsSection';
import { ContentSection } from './sections/ContentSection';
import { DataVisualizationSection } from './sections/DataVisualizationSection';
import { HeaderSection } from './sections/HeaderSection';
import { InsightsSection } from './sections/InsightsSection';
import { NavigationSection } from './sections/NavigationSection';
import { OverviewSection } from './sections/OverviewSection';
import { StatisticsSection } from './sections/StatisticsSection';
import { TagsSection } from './sections/TagsSection';
import { CampaignKeywordsProvider, useCampaignKeywords } from '@/contexts/CampaignKeywordsContext';

// Component con để sử dụng context
const CampaignDetailContent = (): JSX.Element => {
  const { loading, allTagKeyword } = useCampaignKeywords();


  return (
    <>
      <div className="font-bodytext-sm-semibold text-[#141416]">
        Total Keyword: {loading ? "Loading..." : allTagKeyword.length.toLocaleString()}
      </div>
    </>
  );
};

export const CampaignDetail = (): JSX.Element => {
  const [, setLocation] = useLocation();
  const today = new Date();
  const sevenDaysAgo = new Date(today);
  sevenDaysAgo.setDate(today.getDate() - 30);
  const [dateRange, setDateRange] = useState<DateRange>({
    start: new Date(sevenDaysAgo),
    end: new Date(today),
  });

  const handleBackClick = () => {
    setLocation('/');
  };

  const handleDateRangeChange = (range: DateRange) => {
    setDateRange(range);
  };



  return (
    <CampaignKeywordsProvider>
      <div className="flex flex-col w-full items-start relative bg-[#fdfdfd]">
        <NavigationSection />
        <div className="container flex flex-col w-full items-start gap-6 px-[142px] py-6 relative">
        <Button
          variant="outline"
          className="h-10 gap-2 px-3 py-2 rounded-2xl"
          onClick={handleBackClick}
        >
          <ArrowLeftIcon className="w-5 h-5" />
          <span className="font-bodytext-sm-md text-[#141416]">Back</span>
        </Button>

        <HeaderSection />

        <CampaignDetailContent />

        <TagsSection />

        <div className="flex w-full items-start justify-between relative">
          <h3 className="font-heading-h3 text-[#141416]">Overview</h3>

          <DateRangePicker
            value={dateRange}
            onChange={handleDateRangeChange}
            placeholder="Last 30 days"
          />
        </div>

        <StatisticsSection dateRange={dateRange} />
        <DataVisualizationSection dateRange={dateRange} />
        <AnalyticsSection dateRange={dateRange} />
        <OverviewSection dateRange={dateRange} />
        <ContentSection dateRange={dateRange} />
        <InsightsSection dateRange={dateRange} />
      </div>

        <footer className="flex w-full items-center justify-center gap-2.5 px-[142px] py-6 border-t border-[#d9dbde]">
          <span className="font-bodytext-md-reg text-[#909498] text-center">
            socialtrack.net
          </span>
        </footer>
      </div>
    </CampaignKeywordsProvider>
  );
};
