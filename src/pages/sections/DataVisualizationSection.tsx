import React, { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Doughn<PERSON>, Line } from 'react-chartjs-2';
import {
  ArcElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip
} from 'chart.js';
import { useParams } from 'wouter';
import { campaignService } from '@/api';
import { TotalMentionInteractionResponse } from '@/types/campaign';
import { DateRange } from '@/components/molecules/DateRangePicker';
import { formatDateForAPI } from '@/lib/utils';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface DataVisualizationSectionProps {
  dateRange?: DateRange;
}

export const DataVisualizationSection = ({ dateRange }: DataVisualizationSectionProps): JSX.Element => {
  const params = useParams();
  const campaignId = params.id;

  // State for mentions & interactions data
  const [mentionInteractionData, setMentionInteractionData] = useState<TotalMentionInteractionResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for interaction breakdown data
  const [interactionData, setInteractionData] = useState<any[]>([]);
  const [totalInteraction, setTotalInteraction] = useState<number>(0);
  // Get mentions & interactions data
  const getMentionInteractionData = async () => {
    if (!campaignId || !dateRange?.start || !dateRange?.end) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const from_date = formatDateForAPI(dateRange.start);
      const to_date = formatDateForAPI(dateRange.end);

      const response = await campaignService.getCampaignReport({
        id: campaignId,
        endpoint: 'report/total-mention-interaction',
        from_date,
        to_date
      });
      const dataResponse = response as any as TotalMentionInteractionResponse;
      if (response) {
        const apiData = dataResponse as TotalMentionInteractionResponse;
        setMentionInteractionData(apiData);

        // For interaction breakdown, use reactions data and create mock data for comments/shares
        const reactions = apiData.reactions;
        const comments = Math.floor(reactions * 0.6); // 60% of reactions
        const shares = Math.floor(reactions * 0.4); // 40% of reactions

        const total = reactions + comments + shares;
        setTotalInteraction(total);
        // Calculate percentages
        const reactionsPercent = Math.round(( reactions / total ) * 100);
        const commentsPercent = Math.round(( comments / total ) * 100);
        const sharesPercent = 100 - reactionsPercent - commentsPercent; // Ensure total is 100%

        setInteractionData([
          {
            type: 'Reactions',
            percentage: `${reactionsPercent}%`,
            color: 'bg-[#7c47e6]',
            textColor: 'text-[#7c47e6]',
            rawValue: reactions
          },
          {
            type: 'Comments',
            percentage: `${commentsPercent}%`,
            color: 'bg-[#a37eff]',
            textColor: 'text-[#582fa0]',
            rawValue: comments
          },
          {
            type: 'Shares',
            percentage: `${sharesPercent}%`,
            color: 'bg-[#adb0b4]',
            textColor: 'text-[#adb0b4]',
            rawValue: shares
          }
        ]);
      } else {
        setMentionInteractionData(null);
      }
    } catch (err) {
      setError('Failed to load mentions & interactions data');
      setMentionInteractionData(null);
    } finally {
      setLoading(false);
    }
  };

  // Call API when component mounts or dateRange changes
  useEffect(() => {
    getMentionInteractionData();
  }, [campaignId, dateRange]);

  // Legend data for the line chart
  const chartLegend = [
    { type: 'Reactions', color: 'bg-[#4f9ef0]' },
    { type: 'Mentions', color: 'bg-[#2bb684]' }
  ];

  // Doughnut chart data for total interactions
  const doughnutData = {
    labels: interactionData.map(item => item.type),
    datasets: [
      {
        data: interactionData.map(item => item.rawValue), // Use raw values instead of percentages
        backgroundColor: interactionData.map(item => item.color.replace('bg-[', '').replace(']', '')),
        borderWidth: 0,
        cutout: '60%'
      }
    ]
  };

  const textCenterDonut = {
    id: 'textCenterDonut',
    afterDatasetsDraw(chart: any, args: any, options: any) {
      const { ctx, chartArea } = chart;

      if (!chartArea) {
        return;
      }

      ctx.save();

      // Calculate center position
      const centerX = ( chartArea.left + chartArea.right ) / 2;
      const centerY = ( chartArea.top + chartArea.bottom ) / 2;

      // Draw "Total" text
      ctx.fillStyle = '#4e5255';
      ctx.font = '12px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('Total', centerX, centerY - 12);

      // Draw total value
      ctx.fillStyle = '#141416';
      ctx.font = 'bold 24px Inter, sans-serif';
      ctx.fillText(totalInteraction.toLocaleString(), centerX, centerY + 8);

      ctx.restore();
    }
  };
  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#333',
        borderWidth: 1,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            const dataIndex = context.dataIndex;
            const rawValue = interactionData[dataIndex]?.rawValue || 0;
            return `${context.label}: ${rawValue} (${interactionData[dataIndex].percentage})`;
          }
        }
      }
    },
    interaction: {
      intersect: false,
      mode: 'nearest' as const
    },
    hover: {
      mode: 'nearest' as const,
      intersect: false
    },
    elements: {
      arc: {
        borderWidth: 2,
        hoverBorderWidth: 4,
        hoverBorderColor: '#fff'
      }
    }
  };

  // Line chart data for mentions & interactions
  const lineData = {
    labels: mentionInteractionData?.series.labels.map((label) => {
      const date = new Date(label);

      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    }) || [],
    datasets: [
      {
        label: 'Reactions',
        data: mentionInteractionData?.series.reactions || [],
        borderColor: '#4f9ef0',
        backgroundColor: '#4f9ef0',
        borderWidth: 2,
        fill: false,
        tension: 0.4
      },
      {
        label: 'Mentions',
        data: mentionInteractionData?.series.mentions || [],
        borderColor: '#2bb684',
        backgroundColor: '#2bb684',
        borderWidth: 2,
        fill: false,
        tension: 0.4
      }
    ]
  };

  const lineOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false
      }
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false
        },
        ticks: {
          color: '#4e5255',
          font: {
            size: 12
          },
          maxRotation: 45
        }
      },
      y: {
        display: true,
        grid: {
          color: '#f0f0f0'
        },
        ticks: {
          color: '#909498',
          font: {
            size: 12
          },
          callback: function(value: any) {
            if (value >= 1000) {
              return ( value / 1000 ) + 'K';
            }
            return value;
          }
        },
        min: 0
      }
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false
    }
  };

  return (
    <div className="flex items-start gap-6 w-full">
      {/* Total Interactions Card */}
      <Card className="border border-[#c5c8cb] rounded-[26px] bg-[#fdfdfd]">
        <CardContent className="flex flex-col items-start gap-6 p-6 h-[381px]">
          <div className="w-full">
            <h3 className="font-bodytext-md-md text-[#4e5255] text-[length:var(--bodytext-md-md-font-size)] tracking-[var(--bodytext-md-md-letter-spacing)] leading-[var(--bodytext-md-md-line-height)]">
              Total Interactions
            </h3>
          </div>

          <div className="flex flex-col w-[400px] items-center justify-between flex-1">
            {/* Interactive Doughnut Chart */}
            <div className="relative w-[172px] h-[172px]">
              <Doughnut
                key={totalInteraction}
                data={doughnutData}
                options={doughnutOptions}
                plugins={[textCenterDonut]}
              />
            </div>

            {/* Legend and Percentages */}
            <div className="flex flex-wrap items-start justify-center gap-3">
              {interactionData.map((item, index) => (
                <div
                  key={`interaction-${index}`}
                  className="flex items-center gap-3 w-fit"
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-4 h-4 ${item.color} rounded`} />
                    <span className="font-bodytext-sm-md text-[#4e5255] text-[length:var(--bodytext-sm-md-font-size)] tracking-[var(--bodytext-sm-md-letter-spacing)] leading-[var(--bodytext-sm-md-line-height)]">
                      {item.type}
                    </span>
                  </div>
                  <span
                    className={`text-md ${item.textColor} tracking-[var(--heading-h4-letter-spacing)] leading-[var(--heading-h4-line-height)]`}
                  >
                    {item.percentage}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Mentions & Interactions Card */}
      <Card className="flex-1 border border-[#c5c8cb] rounded-[26px] bg-[#fdfdfd]">
        <CardContent className="flex flex-col items-start gap-6 p-6 h-[381px]">
          <div className="w-full">
            <h3 className="font-bodytext-md-md text-[#4e5255] text-[length:var(--bodytext-md-md-font-size)] tracking-[var(--bodytext-md-md-letter-spacing)] leading-[var(--bodytext-md-md-line-height)]">
              Mentions &amp; Interactions
            </h3>
          </div>

          {/* Interactive Line Chart */}
          <div className="h-[241px] w-full">
            {!dateRange?.start || !dateRange?.end ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-[#909498] font-bodytext-sm-md">Please select a date range to view chart</div>
              </div>
            ) : loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-[#909498] font-bodytext-sm-md">Loading chart data...</div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-red-500 font-bodytext-sm-md">{error}</div>
              </div>
            ) : !mentionInteractionData ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-[#909498] font-bodytext-sm-md">No data available for this period</div>
              </div>
            ) : (
              <Line data={lineData} options={lineOptions} />
            )}
          </div>

          {/* Chart Legend */}
          <div className="flex flex-wrap items-start gap-[16px_16px] px-4 py-0 w-full">
            {chartLegend.map((item, index) => (
              <div
                key={`legend-${index}`}
                className="inline-flex items-center gap-2"
              >
                <div className="inline-flex items-center gap-1">
                  <div className={`w-4 h-4 ${item.color} rounded`} />
                  <span className="font-bodytext-sm-md text-[#8c97a7] text-[length:var(--bodytext-sm-md-font-size)] tracking-[var(--bodytext-sm-md-letter-spacing)] leading-[var(--bodytext-sm-md-line-height)]">
                    {item.type}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
