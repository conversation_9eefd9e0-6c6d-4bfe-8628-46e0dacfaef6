import React from 'react';
import { DateRange } from '@/components/molecules/DateRangePicker';
import { TotalSource } from '@/components/Dashboard/TotalSource';
import { TotalEngagement } from '@/components/Dashboard/TotalEngagement';
import { TotalMentions } from '@/components/Dashboard/TotalMentions';
//

interface StatisticsSectionProps {
  dateRange?: DateRange;
}

export const StatisticsSection = ({ dateRange }: StatisticsSectionProps): JSX.Element => {

  return (
    <section className="flex items-start gap-6 relative w-full">
      <TotalMentions dateRange={dateRange} />

      <div className="flex flex-col w-[335px] gap-6 self-stretch">
        <TotalSource dateRange={dateRange} />
        <TotalEngagement dateRange={dateRange} />
      </div>
    </section>
  );
};
