import React from "react";
import { Header } from "@/components/organisms/Header";
import { useCampaignKeywords } from '@/contexts/CampaignKeywordsContext';

export const HeaderSection = (): JSX.Element => {
  const { campaignDetail, loading } = useCampaignKeywords();

  const campaignName = loading
    ? "Loading..."
    : campaignDetail?.name || "Campaign Detail";

  return (
    <Header title={campaignName} />
  );
};
