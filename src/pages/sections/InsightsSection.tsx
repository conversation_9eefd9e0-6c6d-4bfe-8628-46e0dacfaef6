import React from 'react';
import { ArcElement, Chart as <PERSON><PERSON><PERSON>, Legend, Tooltip } from 'chart.js';
import { DateRange } from '@/components/molecules/DateRangePicker';
import { UserIntentClassification } from '@/components/Dashboard/UserIntentClassification';
import { TopMentions } from '@/components/Dashboard/TopMentions';

// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

interface InsightsSectionProps {
  dateRange?: DateRange;
}

export const InsightsSection = ({ dateRange }: InsightsSectionProps): JSX.Element => {

  return (
    <section className="flex gap-6 w-full">
      <TopMentions dateRange={dateRange} />
      <UserIntentClassification dateRange={dateRange} />
    </section>
  );
};
