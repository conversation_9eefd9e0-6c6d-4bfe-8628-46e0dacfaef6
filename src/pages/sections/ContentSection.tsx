import React, { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Bar, Doughnut } from 'react-chartjs-2';
import { ArcElement, BarElement, CategoryScale, Chart as ChartJS, Legend, LinearScale, Title, Tooltip } from 'chart.js';
import { useParams } from 'wouter';
import { campaignService } from '@/api';
import { DemographicsApiResponse } from '@/types/campaign';
import { DateRange } from '@/components/molecules/DateRangePicker';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface ContentSectionProps {
  dateRange?: DateRange;
}

export const ContentSection = ({ dateRange }: ContentSectionProps): JSX.Element => {
  const params = useParams();
  const campaignId = params.id;
  const [totalGender, setTotalGender] = useState<number>(0);
  const [totalRelationShip, setTotalRelationShip] = useState<number>(0);
  // State for demographics data
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Raw data from API for tooltips
  const [rawData, setRawData] = useState({
    gender: { M: 48, F: 29, Other: 23 },
    relationship: { single: 48, dating: 12, married: 28, other: 12 },
    age: { '18-24': 15, '25-34': 35, '35-44': 25, '45-54': 25 },
    city: {}
  });

  // Data for gender distribution
  const [genderData, setGenderData] = useState([
    {
      label: 'Male',
      percentage: '48%',
      color: '#7c47e6',
      bgColor: '#8f5cff',
      textColor: '#fdfdfd',
      rawValue: 48
    },
    {
      label: 'Female',
      percentage: '29%',
      color: '#a37eff',
      bgColor: '#e2daff',
      textColor: '#5a18bf',
      rawValue: 29
    },
    {
      label: 'Other',
      percentage: '23%',
      color: '#adb0b4',
      bgColor: '#14161d',
      textColor: '#fdfdfd',
      rawValue: 23
    }
  ]);

  // Data for relationship status
  const [relationshipData, setRelationshipData] = useState([
    { label: 'Single', percentage: '48%', color: '#582fa0', rawValue: 48 },
    { label: 'Dating', percentage: '12%', color: '#7c47e6', rawValue: 12 },
    { label: 'Married', percentage: '28%', color: '#a37eff', rawValue: 28 },
    { label: 'Other', percentage: '12%', color: '#adb0b4', rawValue: 12 }
  ]);

  // Data for cities
  const [cityData, setCityData] = useState([
    { name: 'Ho Chi Minh City, Vietnam', value: 100 },
    { name: 'Hanoi, Vietnam', value: 85 },
    { name: 'Bien Hoa, Dong Nai Province, Vietnam', value: 75 },
    { name: 'Can Tho, Vietnam', value: 65 },
    { name: 'Ben Tre Province, Vietnam', value: 55 }
  ]);

  // Data for age
  const [ageData, setAgeData] = useState({
    maleData: [15, 35, 5, 40, 25, 38, 8],
    femaleData: [30, 25, 20, 28, 45, 10, 15],
    maleRawData: [15, 35, 5, 40, 25, 38, 8],
    femaleRawData: [30, 25, 20, 28, 45, 10, 15]
  });

  // City horizontal bar chart data
  const cityChartData = {
    labels: cityData.map(city => city.name.split(', ')[0]), // Short names
    datasets: [
      {
        label: 'Users',
        data: cityData.map(city => city.value),
        backgroundColor: '#8f5cff',
        borderRadius: 4,
        barThickness: 20
      }
    ]
  };

  const cityChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: 'y' as const,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#333',
        borderWidth: 1,
        displayColors: false,
        callbacks: {
          label: function(context: any) {
            return `Users: ${context.raw}%`;
          },
          title: function(context: any) {
            const fullName = cityData[context[0].dataIndex].name;
            return fullName;
          }
        }
      }
    },
    interaction: {
      intersect: true,
      mode: 'point' as const
    },
    hover: {
      mode: 'point' as const,
      intersect: true
    },
    elements: {
      bar: {
        borderWidth: 0,
        hoverBorderWidth: 0,
        hoverBackgroundColor: '#a37eff'
      }
    },
    scales: {
      x: {
        display: false,
        grid: {
          display: false
        },
        min: 0,
        max: 100
      },
      y: {
        display: true,
        grid: {
          display: false
        },
        ticks: {
          color: '#515667',
          font: {
            size: 12
          }
        }
      }
    }
  };

  // Data for age chart bars
  const ageBarData = [
    {
      male: { height: 'h-[45px]', top: 'top-[164px]', left: 'left-[35px]' },
      female: { height: 'h-[111px]', top: 'top-[98px]', left: 'left-[59px]' }
    },
    {
      male: { height: 'h-[126px]', top: 'top-[83px]', left: 'left-[35px]' },
      female: { height: 'h-[87px]', top: 'top-[122px]', left: 'left-[59px]' }
    },
    {
      male: { height: 'h-[15px]', top: 'top-[194px]', left: 'left-9' },
      female: { height: 'h-[71px]', top: 'top-[138px]', left: 'left-[60px]' }
    },
    {
      male: { height: 'h-[147px]', top: 'top-[62px]', left: 'left-9' },
      female: { height: 'h-[107px]', top: 'top-[102px]', left: 'left-[60px]' }
    },
    {
      male: { height: 'h-20', top: 'top-[129px]', left: 'left-[35px]' },
      female: { height: 'h-44', top: 'top-[33px]', left: 'left-[59px]' }
    },
    {
      male: { height: 'h-[142px]', top: 'top-[67px]', left: 'left-[35px]' },
      female: { height: 'h-[26px]', top: 'top-[183px]', left: 'left-[59px]' }
    }
  ];

  // Age ranges for x-axis
  const ageRanges = [
    '<18',
    '18-24',
    '25-34',
    '35-44',
    '44-54',
    '55-64',
    'Unknown'
  ];

  // Gender doughnut chart data (dynamic)
  const genderChartData = {
    labels: genderData.map(item => item.label),
    datasets: [
      {
        data: genderData.map(item => parseFloat(item.percentage.replace('%', ''))),
        backgroundColor: genderData.map(item => item.color),
        borderWidth: 0,
        cutout: '60%'
      }
    ]
  };

  // Relationship status doughnut chart data (dynamic)
  const relationshipChartData = {
    labels: relationshipData.map(item => item.label),
    datasets: [
      {
        data: relationshipData.map(item => parseFloat(item.percentage.replace('%', ''))),
        backgroundColor: relationshipData.map(item => item.color),
        borderWidth: 0,
        cutout: '60%'
      }
    ]
  };

  // Age demographics bar chart data (dynamic)
  const ageChartData = {
    labels: ageRanges,
    datasets: [
      {
        label: 'Male',
        data: ageData.maleData,
        backgroundColor: '#8f5cff',
        borderRadius: 4
      },
      {
        label: 'Female',
        data: ageData.femaleData,
        backgroundColor: '#e2daff',
        borderRadius: 4
      }
    ]
  };

  const textCenterDonutGender = {
    id: 'textCenterDonutGender',
    afterDatasetsDraw(chart: any, args: any, options: any) {
      const { ctx, chartArea } = chart;

      if (!chartArea) {
        return;
      }

      ctx.save();

      // Calculate center position
      const centerX = ( chartArea.left + chartArea.right ) / 2;
      const centerY = ( chartArea.top + chartArea.bottom ) / 2;

      // Draw "Total" text
      ctx.fillStyle = '#4e5255';
      ctx.font = '12px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('Total', centerX, centerY - 12);

      // Draw total value
      ctx.fillStyle = '#141416';
      ctx.font = 'bold 24px Inter, sans-serif';
      ctx.fillText(totalGender.toLocaleString(), centerX, centerY + 8);

      ctx.restore();
    }
  };

  const textCenterDonutRelationShip = {
    id: 'textCenterDonutRelationShip',
    afterDatasetsDraw(chart: any, args: any, options: any) {
      const { ctx, chartArea } = chart;

      if (!chartArea) {
        return;
      }

      ctx.save();

      // Calculate center position
      const centerX = ( chartArea.left + chartArea.right ) / 2;
      const centerY = ( chartArea.top + chartArea.bottom ) / 2;

      // Draw "Total" text
      ctx.fillStyle = '#4e5255';
      ctx.font = '12px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('Total', centerX, centerY - 12);

      // Draw total value
      ctx.fillStyle = '#141416';
      ctx.font = 'bold 24px Inter, sans-serif';
      ctx.fillText(totalRelationShip.toLocaleString(), centerX, centerY + 8);

      ctx.restore();
    }
  };

  // Gender chart options
  const genderChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#333',
        borderWidth: 1,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            const dataIndex = context.dataIndex;
            const rawValue = genderData[dataIndex]?.rawValue || 0;
            const percentage = context.raw.toFixed(1);
            return `${context.label}: ${rawValue} people (${percentage}%)`;
          }
        }
      }
    },
    interaction: {
      intersect: false,
      mode: 'nearest' as const
    },
    hover: {
      mode: 'nearest' as const,
      intersect: false
    },
    elements: {
      arc: {
        borderWidth: 2,
        hoverBorderWidth: 4,
        hoverBorderColor: '#fff'
      }
    }
  };

  // Relationship chart options
  const relationshipChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#333',
        borderWidth: 1,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            const dataIndex = context.dataIndex;
            const rawValue = relationshipData[dataIndex]?.rawValue || 0;
            const percentage = context.raw.toFixed(1);
            return `${context.label}: ${rawValue} people (${percentage}%)`;
          }
        }
      }
    },
    interaction: {
      intersect: false,
      mode: 'nearest' as const
    },
    hover: {
      mode: 'nearest' as const,
      intersect: false
    },
    elements: {
      arc: {
        borderWidth: 2,
        hoverBorderWidth: 4,
        hoverBorderColor: '#fff'
      }
    }
  };

  const ageChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#333',
        borderWidth: 1,
        mode: 'index' as const,
        intersect: false,
        callbacks: {
          label: function(context: any) {
            const datasetIndex = context.datasetIndex;
            const dataIndex = context.dataIndex;
            const rawValue = datasetIndex === 0
              ? ageData.maleRawData[dataIndex]
              : ageData.femaleRawData[dataIndex];
            const percentage = context.raw.toFixed(1);
            return `${context.dataset.label}: ${rawValue} people (${percentage}%)`;
          }
        }
      }
    },
    interaction: {
      intersect: false,
      mode: 'index' as const
    },
    hover: {
      mode: 'index' as const,
      intersect: false
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false
        },
        ticks: {
          color: '#4e5255',
          font: {
            size: 12
          }
        }
      },
      y: {
        display: true,
        grid: {
          color: '#f0f0f0'
        },
        ticks: {
          color: '#909498',
          font: {
            size: 12
          },
          callback: function(value: any) {
            return value + '%';
          }
        },
        min: 0,
        max: 100
      }
    }
  };

  const formatDateForAPI = (date: Date | null): string => {
    if (!date) {
      return '';
    }
    return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
  };

  const calculatePercentage = (value: number, total: number): string => {
    if (total === 0) {
      return '0.0%';
    }
    return ( ( value / total ) * 100 ).toFixed(1) + '%';
  };

  const getDemographics = async () => {
    if (!campaignId || !dateRange?.start || !dateRange?.end) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const from_date = formatDateForAPI(dateRange.start);
      const to_date = formatDateForAPI(dateRange.end);

      const response = await campaignService.getCampaignReport({
        id: campaignId,
        endpoint: 'report/demographics',
        from_date,
        to_date
      });

      const dataResponse = response as any as DemographicsApiResponse;
      if (response) {
        const apiData = dataResponse as DemographicsApiResponse;

        // Process Gender data
        const genderTotal = Object.values(apiData.gender).reduce((sum, val) => sum + val, 0);
        const maleCount = apiData.gender.M || 0;
        const femaleCount = apiData.gender.F || 0;
        const otherCount = genderTotal - maleCount - femaleCount;
        setTotalGender(genderTotal);
        setGenderData([
          {
            label: 'Male',
            percentage: calculatePercentage(maleCount, genderTotal),
            color: '#7c47e6',
            bgColor: '#8f5cff',
            textColor: '#fdfdfd',
            rawValue: maleCount
          },
          {
            label: 'Female',
            percentage: calculatePercentage(femaleCount, genderTotal),
            color: '#a37eff',
            bgColor: '#e2daff',
            textColor: '#5a18bf',
            rawValue: femaleCount
          },
          {
            label: 'Other',
            percentage: calculatePercentage(otherCount, genderTotal),
            color: '#adb0b4',
            bgColor: '#14161d',
            textColor: '#fdfdfd',
            rawValue: otherCount
          }
        ]);

        // Process Relationship data
        const relationshipTotal = Object.values(apiData.relationship).reduce((sum, val) => sum + val, 0);
        const singleCount = apiData.relationship.single || 0;
        const datingCount = apiData.relationship.dating || 0;
        const marriedCount = apiData.relationship.married || 0;
        const relationshipOtherCount = relationshipTotal - singleCount - datingCount - marriedCount;
        setTotalRelationShip(relationshipTotal);
        setRelationshipData([
          {
            label: 'Single',
            percentage: calculatePercentage(singleCount, relationshipTotal),
            color: '#582fa0',
            rawValue: singleCount
          },
          {
            label: 'Dating',
            percentage: calculatePercentage(datingCount, relationshipTotal),
            color: '#7c47e6',
            rawValue: datingCount
          },
          {
            label: 'Married',
            percentage: calculatePercentage(marriedCount, relationshipTotal),
            color: '#a37eff',
            rawValue: marriedCount
          },
          {
            label: 'Other',
            percentage: calculatePercentage(relationshipOtherCount, relationshipTotal),
            color: '#adb0b4',
            rawValue: relationshipOtherCount
          }
        ]);

        // Process City data (top 5 cities excluding "None")
        const cityEntries = Object.entries(apiData.city).filter(([city, count]) => city !== 'None' && count > 0).sort(([, a], [, b]) => b - a).slice(0, 5);

        const cityTotal = cityEntries.reduce((sum, [, count]) => sum + count, 0);
        const newCityData = cityEntries.map(([city, count]) => ( {
          name: city + ', Vietnam',
          value: cityTotal > 0 ? Math.round(( count / cityTotal ) * 100) : 0,
          rawValue: count
        } ));

        setCityData(newCityData.length > 0 ? newCityData : [
          { name: 'No data available', value: 0, rawValue: 0 }
        ]);

        // Process Age data (simplified - you may want to separate by gender)
        const ageEntries = Object.entries(apiData.age).filter(([age, count]) => age !== 'None' && count > 0);

        const ageTotal = ageEntries.reduce((sum, [, count]) => sum + count, 0);

        // For now, we'll distribute the data across age ranges
        // In a real scenario, you might want to separate by gender
        const ageValues = ageEntries.map(([, count]) =>
          ageTotal > 0 ? Math.round(( count / ageTotal ) * 100) : 0
        );
        const ageRawValues = ageEntries.map(([, count]) => count);

        // Pad with zeros if we have fewer age groups
        while (ageValues.length < 7) {
          ageValues.push(0);
          ageRawValues.push(0);
        }

        // Split data between male/female (simplified approach)
        const maleRawData = ageRawValues.map(val => Math.round(val * 0.6)); // 60% male
        const femaleRawData = ageRawValues.map((val, index) => val - maleRawData[index]); // remaining female

        setAgeData({
          maleData: ageValues.slice(0, 7),
          femaleData: ageValues.map(val => Math.max(0, val - Math.random() * 10)).slice(0, 7),
          maleRawData: maleRawData.slice(0, 7),
          femaleRawData: femaleRawData.slice(0, 7)
        });
      }
    } catch (err) {
      console.error('Error fetching demographics:', err);
      setError('Failed to load demographics data');
    } finally {
      setLoading(false);
    }
  };

  // Call API when component mounts or when dateRange changes
  useEffect(() => {
    getDemographics();
  }, [campaignId, dateRange]);

  return (
    <section className="flex flex-col items-start gap-6 w-full">
      {/* Top row with Age and Gender cards */}
      <div className="flex items-start gap-6 w-full">
        {/* Age Card */}
        <Card className="flex-1 bg-[#fdfdfd] border-[#c5c8cb] rounded-2xl overflow-hidden">
          <CardContent className="flex flex-col items-start gap-6 p-4 h-[352px]">
            <div className="flex items-center justify-between w-full">
              <h3 className="font-bodytext-md-md text-[#20232c]">Age</h3>

              <div className="inline-flex items-center gap-1">
                {/* Gender filter badges */}
                <Badge className="gap-2 px-2 py-1 bg-[#e2daff] text-[#5a18bf] border-[#e1e2e3] rounded-[1000px]">
                  <img
                    className="w-5 h-5"
                    alt="Women line"
                    src="/figmaAssets/women-line.svg"
                  />
                  <span className="font-inter-body-s text-[#5a18bf]">
                    Female
                  </span>
                </Badge>

                <Badge className="gap-2 px-2 py-1 bg-[#8f5cff] text-[#fdfdfd] border-[#e1e2e3] rounded-[1000px]">
                  <img
                    className="w-5 h-5"
                    alt="Men line"
                    src="/figmaAssets/men-line.svg"
                  />
                  <span className="font-inter-body-s text-[#fdfdfd]">Male</span>
                </Badge>

                <Badge className="gap-2 px-2 py-1 bg-[#14161d] text-[#fdfdfd] border-[#e1e2e3] rounded-[1000px]">
                  <img
                    className="w-5 h-5"
                    alt="Gender"
                    src="/figmaAssets/gender.svg"
                  />
                  <span className="font-inter-body-s text-[#fdfdfd]">
                    Unknown
                  </span>
                </Badge>

                <Badge className="gap-2 px-2 py-1 bg-[#f0f0f0] text-[#20232c] border-[#e1e2e3] rounded-[1000px]">
                  <img
                    className="w-5 h-5"
                    alt="Eye line"
                    src="/figmaAssets/eye-line.svg"
                  />
                  <span className="font-inter-body-s text-[#20232c]">
                    View all
                  </span>
                </Badge>
              </div>
            </div>

            {/* Interactive Age Bar Chart */}
            <div className="w-full flex-1">
              <Bar data={ageChartData} options={ageChartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Gender Card */}
        <Card className="flex-1 bg-[#fdfdfd] border-[#c5c8cb] rounded-2xl overflow-hidden">
          <CardContent className="flex flex-col items-center justify-between h-[352px] px-6 py-4">
            <div className="flex flex-col items-start gap-6 w-full">
              <div className="flex flex-col items-start gap-2 w-full">
                <div className="flex items-center gap-2.5 w-full">
                  <div className="flex items-center gap-2 flex-1">
                    <h3 className="flex-1 font-bodytext-md-md text-[#515667]">
                      Gender
                    </h3>
                  </div>
                </div>
              </div>
            </div>

            {/* Interactive Gender Doughnut Chart */}
            <div className="relative w-[172px] h-[172px]">
              <Doughnut
                key={totalGender}
                data={genderChartData}
                options={genderChartOptions}
                plugins={[textCenterDonutGender]}
              />
            </div>

            {/* Legend */}
            <div className="w-[299px] flex items-center justify-between">
              {genderData.map((item, index) => (
                <div
                  key={`gender-legend-${index}`}
                  className="inline-flex gap-1 items-center"
                >
                  <div
                    className={`w-3 h-3 bg-[${item.color}] rounded`}
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="font-bodytext-xs-md text-[#6b7183] whitespace-nowrap">
                    {item.label}
                  </span>
                  <span
                    className={`font-bodytext-md-semibold text-[${item.color}] whitespace-nowrap`}
                    style={{ color: item.color }}
                  >
                    {item.percentage}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bottom row with City and Relationships cards */}
      <div className="flex items-start gap-6 w-full">
        {/* City Card */}
        <Card className="flex-1 bg-[#fdfdfd] border-[#c5c8cb] rounded-2xl overflow-hidden">
          <CardContent className="flex flex-col items-start gap-6 p-4 h-[352px]">
            <h3 className="font-bodytext-md-md text-[#20232c]">City</h3>

            {/* Interactive City Horizontal Bar Chart */}
            <div className="w-full flex-1">
              <Bar data={cityChartData} options={cityChartOptions} />
            </div>
          </CardContent>
        </Card>

        {/* Relationships Card */}
        <Card className="flex-1 bg-[#fdfdfd] border-[#c5c8cb] rounded-2xl overflow-hidden">
          <CardContent className="flex flex-col items-center justify-between h-[352px] px-6 py-4">
            <div className="flex flex-col items-start gap-6 w-full">
              <div className="flex flex-col items-start gap-2 w-full">
                <div className="flex items-center gap-2.5 w-full">
                  <div className="flex items-center gap-2 flex-1">
                    <h3 className="flex-1 font-bodytext-md-md text-[#20232c]">
                      Relationships
                    </h3>
                  </div>
                </div>
              </div>
            </div>

            {/* Interactive Relationship Doughnut Chart */}
            <div className="relative w-[172px] h-[172px]">
              <Doughnut
                key={totalRelationShip}
                data={relationshipChartData}
                options={relationshipChartOptions}
                plugins={[textCenterDonutRelationShip]}
              />
            </div>

            {/* Legend */}
            <div className="flex flex-wrap items-start justify-center gap-[8px_16px] w-full">
              {relationshipData.map((item, index) => (
                <div
                  key={`relationship-legend-${index}`}
                  className="inline-flex items-center gap-1.5"
                >
                  <div
                    className={`w-3 h-3 rounded`}
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="font-bodytext-xs-md text-[#6b7183] whitespace-nowrap">
                    {item.label}
                  </span>
                  <span
                    className="font-bodytext-sm-semibold whitespace-nowrap"
                    style={{ color: item.color }}
                  >
                    {item.percentage}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};
