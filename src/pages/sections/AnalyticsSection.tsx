import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Doughn<PERSON>, Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { useParams } from 'wouter';
import { campaignService } from '@/api';
import { SentimentAnalysisApiResponse } from '@/types/campaign';
import { DateRange } from '@/components/molecules/DateRangePicker';
import { formatDateForAPI } from '@/lib/utils';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface AnalyticsSectionProps {
  dateRange?: DateRange;
}

export const AnalyticsSection = ({ dateRange }: AnalyticsSectionProps): JSX.Element => {
  const params = useParams();
  const campaignId = params.id;

  const [sentimentData, setSentimentData] = useState<any[]>([]);
  const [totalSentiments, setTotalSentiments] = useState(0);
  const [sentimentRawData, setSentimentRawData] = useState<{positive: number, negative: number, neutral: number}>({
    positive: 0,
    negative: 0,
    neutral: 0
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Chart data states
  const [chartDates, setChartDates] = useState<string[]>(['']);
  const [sentimentChartData, setSentimentChartData] = useState<{labels:string[], datasets: any[]}>({
    labels: [],
    datasets: [],
  });
  const [sentimentLineData, setSentimentLineData] = useState<{labels:string[], datasets: any[]}>({
    labels: chartDates,
    datasets:[],
  });

  const formatDateForDisplay = (dateStr: string): string => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const calculatePercentage = (value: number, total: number): string => {
    if (total === 0) return '0%';
    return Math.round((value / total) * 100) + '%';
  };

  const getSentimentAnalysis = async () => {
    if (!campaignId || !dateRange?.start || !dateRange?.end) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const from_date = formatDateForAPI(dateRange.start);
      const to_date = formatDateForAPI(dateRange.end);

      const response = await campaignService.getCampaignReport({
        id: campaignId,
        endpoint: 'report/sentiment-analysis',
        from_date,
        to_date
      });
      const dataResponse = response as any as SentimentAnalysisApiResponse;
      if (response) {
        const apiData = dataResponse as SentimentAnalysisApiResponse;

        // Calculate total for percentage calculation
        const total = apiData.totals.positive + apiData.totals.negative + apiData.totals.neutral;
        setTotalSentiments(total);

        // Store raw data
        setSentimentRawData({
          positive: apiData.totals.positive,
          negative: apiData.totals.negative,
          neutral: apiData.totals.neutral
        });

        // Update sentiment data with percentages
        setSentimentData([
          {
            label: "Positive",
            percentage: calculatePercentage(apiData.totals.positive, total),
            color: "bg-[#2bb684] text-[#2bb684]",
            rawValue: apiData.totals.positive
          },
          {
            label: "Negative",
            percentage: calculatePercentage(apiData.totals.negative, total),
            color: "bg-[#f84242] text-[#f84242]",
            rawValue: apiData.totals.negative
          },
          {
            label: "Neutral",
            percentage: calculatePercentage(apiData.totals.neutral, total),
            color: "bg-[#c5c8cb] text-[#909498]",
            rawValue: apiData.totals.neutral
          },
        ]);

        // Update donut chart data with raw values
        setSentimentChartData({
          labels: ['Positive', 'Negative', 'Neutral'],
          datasets: [
            {
              data: [
                apiData.totals.positive,
                apiData.totals.negative,
                apiData.totals.neutral
              ],
              backgroundColor: ['#2bb684', '#f84242', '#c5c8cb'],
              borderWidth: 0,
              cutout: '60%',
            },
          ],
        });

        // Format dates for display
        const formattedDates = apiData.series.labels.map(formatDateForDisplay);
        setChartDates(formattedDates);

        // Update line chart data with actual values
        setSentimentLineData({
          labels: formattedDates,
          datasets: [
            {
              label: 'Positive',
              data: apiData.series.positive,
              borderColor: '#2bb684',
              backgroundColor: '#2bb684',
              borderWidth: 2,
              fill: false,
              tension: 0.4,
            },
            {
              label: 'Negative',
              data: apiData.series.negative,
              borderColor: '#f84242',
              backgroundColor: '#f84242',
              borderWidth: 2,
              fill: false,
              tension: 0.4,
            },
            {
              label: 'Neutral',
              data: apiData.series.neutral,
              borderColor: '#c5c8cb',
              backgroundColor: '#c5c8cb',
              borderWidth: 2,
              fill: false,
              tension: 0.4,
            },
          ],
        });
      }
    } catch (err) {
      setError('Failed to load sentiment analysis');
    } finally {
      setLoading(false);
    }
  };

  // Call API when component mounts or when dateRange changes
  useEffect(() => {
    getSentimentAnalysis();
  }, [campaignId, dateRange]);

  // Custom plugin for center text in sentiment donut
  const sentimentCenterText = {
    id: 'sentimentCenterText',
    beforeDatasetDraw(chart: any, args: any, options: any) {
      const { ctx, chartArea } = chart;

      if (!chartArea) return;

      ctx.save();

      // Calculate center position
      const centerX = (chartArea.left + chartArea.right) / 2;
      const centerY = (chartArea.top + chartArea.bottom) / 2;

      // Draw "Total" text
      ctx.fillStyle = '#4e5255';
      ctx.font = '12px Inter, sans-serif';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('Total', centerX, centerY - 12);

      // Draw total value
      ctx.fillStyle = '#141416';
      ctx.font = 'bold 24px Inter, sans-serif';
      ctx.fillText(totalSentiments.toLocaleString(), centerX, centerY + 8);

      ctx.restore();
    }
  };

  const sentimentChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#333',
        borderWidth: 1,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            const dataIndex = context.dataIndex;
            const rawValue = context.raw;
            const percentage = Math.round((rawValue / totalSentiments) * 100);
            return `${context.label}: ${rawValue.toLocaleString()} (${percentage}%)`;
          }
        }
      }
    },
    interaction: {
      intersect: false,
      mode: 'nearest' as const,
    },
    hover: {
      mode: 'nearest' as const,
      intersect: false,
    },
    elements: {
      arc: {
        borderWidth: 2,
        hoverBorderWidth: 4,
        hoverBorderColor: '#fff',
      }
    },
  };

  const sentimentLineOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false,
        },
        ticks: {
          color: '#4e5255',
          font: {
            size: 12,
          },
          maxRotation: 45,
        },
      },
      y: {
        display: true,
        grid: {
          color: '#f0f0f0',
        },
        ticks: {
          color: '#909498',
          font: {
            size: 12,
          },
          callback: function(value: any) {
            if (value >= 1000) {
              return (value / 1000) + 'K';
            }
            return value;
          },
        },
        min: 0,
      },
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false,
    },
  };

  return (
    <section className="flex items-start gap-6 relative self-stretch w-full">
      <Card className="flex-1 border border-solid border-[#c5c8cb] rounded-[26px] bg-[#fdfdfd] overflow-hidden">
        <CardContent className="flex flex-col items-start gap-4 p-6">
          <div className="flex items-start justify-between w-full">
            <div className="flex flex-1 flex-col items-start justify-center gap-1">
              <h3 className="font-bodytext-md-md font-[number:var(--bodytext-md-md-font-weight)] text-[#4e5255] text-[length:var(--bodytext-md-md-font-size)] tracking-[var(--bodytext-md-md-letter-spacing)] leading-[var(--bodytext-md-md-line-height)] [font-style:var(--bodytext-md-md-font-style)]">
                Sentiment Analysis
              </h3>
            </div>
            <div className="flex items-start ml-auto justify-center gap-2">
              {sentimentData.map((item, index) => (
                <div key={index} className="flex items-center gap-3 w-full">
                  <div className="flex items-center gap-3 flex-1 self-stretch">
                    <div
                      className={`relative w-4 h-4 ${item.color.split(" ")[0]} rounded`}
                    />
                    <div className="relative w-fit font-bodytext-sm-md font-[number:var(--bodytext-sm-md-font-weight)] text-[#4e5255] text-[length:var(--bodytext-sm-md-font-size)] tracking-[var(--bodytext-sm-md-letter-spacing)] leading-[var(--bodytext-sm-md-line-height)] whitespace-nowrap [font-style:var(--bodytext-sm-md-font-style)]">
                      {item.label}
                    </div>
                  </div>
                  <div
                    className={`${item.color.split(" ")[1]} tracking-[var(--heading-h4-letter-spacing)] leading-[var(--heading-h4-line-height)] relative w-fit mt-[-1.00px] whitespace-nowrap [font-style:var(--heading-h4-font-style)]`}
                  >
                    {item.percentage}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="flex h-[235px] items-center justify-center gap-16 w-full">
            {!dateRange?.start || !dateRange?.end ? (
              <div className="flex items-center justify-center h-full w-full">
                <div className="text-[#909498] font-bodytext-sm-md">Please select a date range to view sentiment analysis</div>
              </div>
            ) : loading ? (
              <div className="flex items-center justify-center h-full w-full">
                <div className="text-[#909498] font-bodytext-sm-md">Loading sentiment analysis...</div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-full w-full">
                <div className="text-red-500 font-bodytext-sm-md">{error}</div>
              </div>
            ) : (
              <>
                <div className="flex h-[198px] items-center justify-center gap-16 flex-1">
                  {/* Interactive Sentiment Doughnut Chart */}
                  <div className="relative w-[188px] h-[188px]">
                    <Doughnut
                      data={sentimentChartData}
                      options={sentimentChartOptions}
                      plugins={[sentimentCenterText]}
                    />
                  </div>
                </div>

                {/* Interactive Sentiment Line Chart */}
                <div className="flex w-full h-[235px] items-center gap-3 relative">
                  <div className="w-full h-full">
                    <Line data={sentimentLineData} options={sentimentLineOptions} />
                  </div>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </section>
  );
};
