import React, { useState } from 'react';
import { TagList } from '@/components/molecules/TagList';
import { useCampaignKeywords } from '@/contexts/CampaignKeywordsContext';

const MAX_DISPLAY_TAGS = 50;
export const TagsSection = (): JSX.Element => {
  const { loading, error, allTagKeyword } = useCampaignKeywords();
  const [showAllKeywords, setShowAllKeywords] = useState(false);


  // Helper functions for display logic
  const getCurrentTags = () => {
    return allTagKeyword;
  };

  const getCurrentShowAll = () => {
    return showAllKeywords;
  };

  const toggleShowAll = () => {
    setShowAllKeywords(!showAllKeywords);
  };

  const getDisplayTags = () => {
    const currentTags = getCurrentTags();
    const showAll = getCurrentShowAll();

    if (showAll || currentTags.length <= MAX_DISPLAY_TAGS) {
      return currentTags;
    }
    return currentTags.slice(0, MAX_DISPLAY_TAGS);
  };

  const shouldShowToggle = () => {
    const currentTags = getCurrentTags();
    return currentTags.length > MAX_DISPLAY_TAGS;
  };

  return (
    <div className="flex flex-col gap-4 w-full">
      {loading ? (
        <div className="flex items-center justify-center h-32">
          <div className="text-[#909498] font-bodytext-sm-md">Loading tags...</div>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center h-32">
          <div className="text-red-500 font-bodytext-sm-md">{error}</div>
        </div>
      ) : getCurrentTags().length === 0 ? (
        <div className="flex items-center justify-center h-32">
          <div className="text-[#909498] font-bodytext-sm-md">
            No keywords found for this campaign
          </div>
        </div>
      ) : (
        <div className="flex flex-col gap-4">
          {/* Tags Display */}
          <TagList
            tags={getDisplayTags()}
            showToggle={shouldShowToggle()}
            toggleText={getCurrentShowAll() ?
              'Show less' :
              `Show more (${getCurrentTags().length - MAX_DISPLAY_TAGS} more)`}
            onToggle={toggleShowAll}
            isShowAll={getCurrentShowAll()}
          />

          {/* Summary */}
          <div className="text-sm text-[#6b7280]">
            Showing {getDisplayTags().length} of {getCurrentTags().length} keywords
          </div>
        </div>
      )}
    </div>
  );
};
