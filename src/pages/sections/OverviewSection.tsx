import React from 'react';
import { TopicCloud } from '@/components/Dashboard/TopicCloud';
import { DateRange } from '@/components/molecules/DateRangePicker';
import { TrendingHashtag } from '@/components/Dashboard/TrendingHashtag';

interface OverviewSectionProps {
  dateRange?: DateRange;
}

export const OverviewSection = ({ dateRange }: OverviewSectionProps): JSX.Element => {

  return (
    <div className="flex items-start gap-6 relative self-stretch w-full">
      <TrendingHashtag dateRange={dateRange} />
      <TopicCloud dateRange={dateRange} />
    </div>
  );
};
