export interface Tag {
  id: string;
  campaign_id: string;
  tag_type: string;
  tag_value: string;
  created_at: string;
  updated_at: string;
}

export interface Campaign {
  id: string;
  name: string;
  description: string;
  industry: string;
  workspace_id: string;
  segment_pools: any[];
  tags: Tag[];
  created_at?: string;
  updated_at?: string;
}

export interface CampaignProfileAnalysis {
  total: number;
}

export interface TopicCloudToken {
  text: string;
  weight: number;
}

export interface TopicCloudResponse {
  tokens: TopicCloudToken[];
  max_weight: number;
}

export interface TrendingHashtagResponse {
  labels: string[];
  values: string[];
}

export interface SentimentAnalysisApiResponse {
  totals: {
    positive: number;
    negative: number;
    neutral: number;
  };
  donut: {
    labels: string[];
    values: number[];
  };
  series: {
    labels: string[];
    positive: number[];
    negative: number[];
    neutral: number[];
  };
}

export interface TotalSourceResponse {
  curr: number;
  prev: number;
  pct: number;
}

export interface TotalEngagementResponse {
  curr: number;
  prev: number;
  pct: number;
}

export interface TotalMentionsResponse {
  curr: number;
  prev: number;
  pct: number;
  series: {
    labels: string[];
    values: number[];
  };
}

export interface DemographicsApiResponse {
  age: Record<string, number>;
  gender: Record<string, number>;
  city: Record<string, number>;
  relationship: Record<string, number>;
}

export interface TopPostsByMentionsResponse {
  id: string;
  post_id: string;
  author_id: string;
  author_name: string;
  content: string;
  sentiment: string;
  intent: string;
  post_time: string;
  engagement: {
    like: number;
    share: number;
    comment: number;
  };
  created_at: string;
  updated_at: string;
}

export interface TopInfluencersByMentionsResponse {
  author_id: string;
  name: string;
  posts: number;
  mentions: number;
  sentiment: {
    positive: number;
    negative: number;
    neutral: number;
  };
}

export interface CampaignTag {
  id: string;
  campaign_id: string;
  tag_type: string;
  tag_value: string;
  created_at: string;
  updated_at: string;
}

export interface CampaignDataSource {
  id: string;
  campaign_id: string;
  source_type: string;
  url: string;
  name: string;
  size: number;
}

export interface CampaignDetailResponse {
  id: string;
  name: string;
  description: string;
  industry: string;
  workspace_id: string;
  tags: CampaignTag[];
  created_at: string;
  updated_at: string;
  data_sources: CampaignDataSource[];
  segment_pools: any[];
}

export interface TotalMentionInteractionResponse {
  mentions: number;
  reactions: number;
  series: {
    labels: string[];
    mentions: number[];
    reactions: number[];
  };
}

export interface TopInfluencersData {
  avatar: string;
  followers: string;
  handle: string;
  id: string;
  mentions: number;
  name: string;
  sentimentPercentages: {
    positive: number, negative: number, neutral: number
  };
}

export interface UserIntentResponse {
  PROVIDER: number;
  SEEKER: number;
  NEUTRAL: number;
  null: number;
}

export interface TopPostByIntent {
  id: string;
  post_id: string;
  author_id: string;
  author_name: string;
  content: string;
  sentiment: string;
  intent: string;
  post_time: string;
  engagement: {
    like: number;
    share: number;
    comment: number;
  };
  created_at: string;
  updated_at: string;
}

export interface PostIntentData {
  id: string | number;
  author: string;
  time: string;
  platform: string;
  sentiment: string;
  sentimentIcon: string;
  content: string;
  likes: number;
  comments: number;
  shares: number;
  highlightPosition: {left: string, width: string};
}

export type TopPostsByIntentResponse = TopPostByIntent[];

export interface DemographicsGender {
  count: number;
  pct: number;
}

export interface DemographicsData {
  male: DemographicsGender;
  female: DemographicsGender;
  unknown?: DemographicsGender; // Added unknown gender field
  total: number;
}

export interface HashtagAnalysisItem {
  tag: string;
  mentions: number;
  pct_change?: number | null; // Made optional and nullable to handle API variations
}

export type HashtagAnalysisResponse = HashtagAnalysisItem[];

export interface SentimentDistributionItem {
  count: number;
  pct: number;
}

export interface SentimentDistribution {
  positive: SentimentDistributionItem;
  neutral: SentimentDistributionItem;
  negative?: SentimentDistributionItem; // Made optional since it might be missing
  null?: SentimentDistributionItem; // Added null field from new API response
  total: number;
}

export interface SentimentTrend {
  labels: string[];
  positive: number[];
  neutral: number[];
  negative: number[];
}

export interface SentimentHeadline {
  leading_sentiment: 'positive' | 'neutral' | 'negative';
  pct: number;
}

export interface SentimentAnalysisResponse {
  distribution: SentimentDistribution;
  trend: SentimentTrend;
  headline: SentimentHeadline;
}

export interface PerformanceMetric {
  total: number;
  pct_change_vs_prev: number;
}

export interface PerformanceSummary {
  reach: PerformanceMetric;
  mention: PerformanceMetric;
}

export interface PerformanceSeries {
  labels: string[];
  reach: number[];
  mention: number[];
}

export interface PerformanceOverviewResponse {
  summary: PerformanceSummary;
  series: PerformanceSeries;
}

export interface IntegrationsSourceItem {
  source: string;
  mentions: number;
}

export interface IntegrationsSourcesResponse {
  summary: {
    total_mentions: number;
  };
  distribution: IntegrationsSourceItem[];
  top_source: IntegrationsSourceItem;
}

export interface MentionPostEngagement {
  like: number;
  share: number;
  comment: number;
}

export interface MentionPost {
  id: string;
  source_id: string;
  campaign_id: string;
  post_id: string;
  author_id: string;
  content: string;
  sentiment: 'positive' | 'negative' | 'neutral' | null;
  engagement: MentionPostEngagement;
  post_time: string;
  created_at: string;
  updated_at: string;
}

export type MentionPostsResponse = MentionPost[];

export interface MentionAuthor {
  author_id: string;
  author_name: string;
  latest_post: string;
}

export type MentionAuthorsResponse = MentionAuthor[];

// Top Comments Types
export interface CommentAuthor {
  id: string;
  name: string | null;
}

export interface Comment {
  snapshot_id: string;
  comment: string;
  published_at: string;
  author: CommentAuthor;
  channel: string;
}

export interface TopCommentsResponse {
  positive: Comment[];
  negative: Comment[];
}

// Relevant Keywords API Response (same structure as TopicCloud)
export type RelevantKeywordsResponse = TopicCloudResponse;
