import { useEffect, useState } from 'react';
import { fetchAvatarUrl } from '@/api';

const useAvatar = (uid: string, name: string) => {
  const [avatar, setAvatar] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const getAvatar = async (uid: string, name: string) => {
    if (!uid) {
      setLoading(false);
      return;
    }
    setLoading(true);
    return fetchAvatarUrl(uid, `Author ${name}`).then((data) => {
      setAvatar(data);
    });
  };
  useEffect(() => {
    getAvatar(uid, name).finally(() => {
      setLoading(false);

    });
  }, [uid, name]);
  return { avatar, loading };
};
export default useAvatar;
