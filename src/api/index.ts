// Export configuration
export * from './config';

// Export base service
export { default as BaseApiService } from './base';

// Export axios instance
export { default as apiClient } from './axios';

// Export services
export { default as campaignService } from './services/campaignService';
export { fetchAvatarUrl } from './services/avatarService';

// Export types
export type { ApiResponse } from './base';
export type {
  Tag,
  Campaign,
  TopicCloudResponse,
  TopicCloudToken,
  TrendingHashtagResponse,
  SentimentAnalysisApiResponse,
  TotalSourceResponse,
  TotalEngagementResponse,
  TotalMentionsResponse,
  DemographicsApiResponse,
  TopPostsByMentionsResponse,
  TopInfluencersByMentionsResponse,
  TotalMentionInteractionResponse,
  CampaignDetailResponse,
  CampaignTag,
  CampaignDataSource,
  UserIntentResponse,
  TopPostByIntent,
  TopPostsByIntentResponse,
  DemographicsData,
  DemographicsGender,
  HashtagAnalysisResponse,
  HashtagAnalysisItem,
  SentimentAnalysisResponse,
  SentimentDistribution,
  SentimentDistributionItem,
  SentimentTrend,
  SentimentHeadline,
  PerformanceOverviewResponse,
  PerformanceSummary,
  PerformanceSeries,
  PerformanceMetric,
  IntegrationsSourcesResponse,
  IntegrationsSourceItem,
  MentionPost,
  MentionPostEngagement,
  MentionPostsResponse,
  MentionAuthor,
  MentionAuthorsResponse,
  Comment,
  CommentAuthor,
  TopCommentsResponse
} from '../types/campaign';
