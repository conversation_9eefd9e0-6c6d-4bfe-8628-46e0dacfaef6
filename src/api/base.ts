import apiClient from './axios';
import { AxiosResponse, AxiosRequestConfig } from 'axios';
import { API_URL_V1, API_URL_V2 } from './config';

export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
}

export interface RequestConfig extends AxiosRequestConfig {
  isV2?: boolean;
}

class BaseApiService {
  // GET method
  async get<T = any>(
    endpoint: string,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      // Create a new axios instance with the appropriate base URL
      const client = this.getClient(config?.isV2);
      const response: AxiosResponse<ApiResponse<T>> = await client.get(
        endpoint,
        config
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Helper method to get the appropriate client
  private getClient(isV2?: boolean) {
    if (isV2) {
      // Create V2 client with V2 base URL
      const v2Client = apiClient.create({
        baseURL: API_URL_V2,
        timeout: apiClient.defaults.timeout,
        headers: apiClient.defaults.headers,
      });

      // Copy interceptors from main client
      v2Client.interceptors.request = apiClient.interceptors.request;
      v2Client.interceptors.response = apiClient.interceptors.response;

      return v2Client;
    }
    return apiClient;
  }

  // POST method
  async post<T = any>(
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const client = this.getClient(config?.isV2);
      const response: AxiosResponse<ApiResponse<T>> = await client.post(
        endpoint,
        data,
        config
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // PUT method
  async put<T = any>(
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const client = this.getClient(config?.isV2);
      const response: AxiosResponse<ApiResponse<T>> = await client.put(
        endpoint,
        data,
        config
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // PATCH method
  async patch<T = any>(
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const client = this.getClient(config?.isV2);
      const response: AxiosResponse<ApiResponse<T>> = await client.patch(
        endpoint,
        data,
        config
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // DELETE method
  async delete<T = any>(
    endpoint: string,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const client = this.getClient(config?.isV2);
      const response: AxiosResponse<ApiResponse<T>> = await client.delete(
        endpoint,
        config
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Error handler
  private handleError(error: any): Error {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.message || error.response.statusText;
      return new Error(`API Error (${error.response.status}): ${message}`);
    } else if (error.request) {
      // Request was made but no response received
      return new Error('Network Error: No response from server');
    } else {
      // Something else happened
      return new Error(`Request Error: ${error.message}`);
    }
  }
}

export default new BaseApiService();
