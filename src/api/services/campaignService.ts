import { Campaign } from '@/types/campaign.ts';
import BaseApiService from '../base';
import { END_POINTS } from '@/api';

class CampaignService {
  // Get all campaigns with pagination support
  async getCampaigns(params?: {
    page?: number;
    limit?: number;
    search?: string;
    industry?: string;
  }) {
    const queryParams = {
      page: params?.page || 1,
      limit: params?.limit || 20,
      ...(params?.search && { search: params.search }),
      ...(params?.industry && { industry: params.industry }),
    };

    return BaseApiService.get<Campaign[]>(END_POINTS.campaign, {
      params: queryParams
    });
  }

  // Get campaign by ID
  async getCampaign(id: string) {
    return BaseApiService.get<Campaign>(`${END_POINTS.campaign}${id}`);
  }


  // custom endpoint to get campaigns report (uses V2 API)
  async getCampaignReport(data: {
    id: string,
    endpoint: string,
    from_date?: string,
    to_date?: string,
    params?: Record<string, any>
  }) {
    const { id, endpoint, from_date, to_date, params = {} } = data;

    // Add date parameters if provided
    const queryParams = { ...params };
    if (from_date) queryParams.from_date = from_date;
    if (to_date) queryParams.to_date = to_date;

    return BaseApiService.get<Campaign>(`${END_POINTS.campaign}${id}/${endpoint}`, {
      params: queryParams,
      isV2: true  // Use V2 API for reports
    });
  }
}

export default new CampaignService();
