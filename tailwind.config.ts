module.exports = {
  content: ["./index.html", "./src/**/*.{js,jsx,ts,tsx}",
    "app/**/*.{ts,tsx}",
    "components/**/*.{ts,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        "body-l-semi-bold": "var(--body-l-semi-bold-font-family)",
        "bodytext-lg-semibold": "var(--bodytext-lg-semibold-font-family)",
        "bodytext-md-md": "var(--bodytext-md-md-font-family)",
        "bodytext-md-reg": "var(--bodytext-md-reg-font-family)",
        "bodytext-md-semibold": "var(--bodytext-md-semibold-font-family)",
        "bodytext-sm-md": "var(--bodytext-sm-md-font-family)",
        "bodytext-sm-reg": "var(--bodytext-sm-reg-font-family)",
        "bodytext-sm-semibold": "var(--bodytext-sm-semibold-font-family)",
        "bodytext-xs-md": "var(--bodytext-xs-md-font-family)",
        "bodytext-xs-reg": "var(--bodytext-xs-reg-font-family)",
        "caption-l-medium": "var(--caption-l-medium-font-family)",
        "caption-l-regular": "var(--caption-l-regular-font-family)",
        "caption-m-semi-bold": "var(--caption-m-semi-bold-font-family)",
        "heading-h1": "var(--heading-h1-font-family)",
        "heading-h2": "var(--heading-h2-font-family)",
        "heading-h3": "var(--heading-h3-font-family)",
        "heading-h4": "var(--heading-h4-font-family)",
        "inter-body-s": "var(--inter-body-s-font-family)",
        "inter-caption-m": "var(--inter-caption-m-font-family)",
        "link-sm": "var(--link-sm-font-family)",
        "link-xs": "var(--link-xs-font-family)",
        "overline-regular": "var(--overline-regular-font-family)",
        "title-md": "var(--title-md-font-family)",
        sans: [
          "ui-sans-serif",
          "system-ui",
          "sans-serif",
          '"Apple Color Emoji"',
          '"Segoe UI Emoji"',
          '"Segoe UI Symbol"',
          '"Noto Color Emoji"',
        ],
      },
      boxShadow: {
        "hard-shadow-sm": "var(--hard-shadow-sm)",
        "shadow-md": "var(--shadow-md)",
        "shadow-sm": "var(--shadow-sm)",
        "soft-shadow-sm": "var(--soft-shadow-sm)",
        "soft-shadow-xs": "var(--soft-shadow-xs)",
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
    container: { center: true, padding: "2rem", screens: { "2xl": "1400px" } },
  },
  plugins: [require("tailwindcss-animate"), require("@tailwindcss/typography")],
  darkMode: ["class"],
};
