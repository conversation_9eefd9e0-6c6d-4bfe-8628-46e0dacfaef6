run = "npm run dev"
modules = ["nodejs-20"]
hidden = [
    ".config",
    "tsconfig.json",
    "tsconfig.node.json",
    "vite.config.js",
    ".gitignore",
]
entrypoint = "src/App.tsx"

[nix]
channel = "stable-24_05"

[unitTest]
language = "nodejs"

[deployment]
deploymentTarget = "cloudrun"
build = ["npm", "run", "build"]
run = ["sh", "-c", "npm run preview"]

[[ports]]
localPort = 4173
externalPort = 80

[[ports]]
localPort = 5173
externalPort = 5173

[[ports]]
localPort = 5174
externalPort = 3000

[[ports]]
localPort = 5175
externalPort = 3001
